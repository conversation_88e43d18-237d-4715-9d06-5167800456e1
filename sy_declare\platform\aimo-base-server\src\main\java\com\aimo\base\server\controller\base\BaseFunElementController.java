package com.aimo.base.server.controller.base;

import com.aimo.base.client.model.base.BaseFunElement;
import com.aimo.base.server.service.base.IBaseFunElementService;
import com.aimo.common.model.ResultBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 功能字段元素控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Api(tags = "功能字段元素管理")
@RestController
@RequestMapping("fun-element")
public class BaseFunElementController {

    @Resource
    private IBaseFunElementService baseFunElementService;

    /**
     * 获取字段列表
     */
    @ApiOperation(value = "获取字段列表", notes = "根据功能类型获取字段列表")
    @GetMapping("/list")
    public ResultBody<List<BaseFunElement>> list(@RequestParam(required = false) Integer funType,
                                                  @RequestParam(required = false) Integer type) {
        List<BaseFunElement> list;
        if (funType != null && type != null) {
            list = baseFunElementService.getByFunTypeAndType(funType, type);
        } else if (funType != null) {
            list = baseFunElementService.getByFunType(funType);
        } else {
            list = baseFunElementService.list();
        }
        return ResultBody.ok(list);
    }

    /**
     * 根据功能类型获取字段列表
     */
    @ApiOperation(value = "根据功能类型获取字段列表", notes = "根据功能类型获取字段列表")
    @GetMapping("/by-fun-type/{funType}")
    public ResultBody<List<BaseFunElement>> getByFunType(@PathVariable Integer funType) {
        List<BaseFunElement> list = baseFunElementService.getByFunType(funType);
        return ResultBody.ok(list);
    }

    /**
     * 根据ID查找数据
     */
    @ApiOperation(value = "根据ID查找字段数据", notes = "根据ID查找字段数据")
    @GetMapping("/{id}")
    public ResultBody<BaseFunElement> get(@PathVariable Long id) {
        BaseFunElement element = baseFunElementService.getById(id);
        return ResultBody.ok(element);
    }

    /**
     * 新增字段
     */
    @ApiOperation(value = "新增字段", notes = "新增功能字段元素")
    @PostMapping
    public ResultBody<Boolean> create(@RequestBody BaseFunElement element) {
        boolean result = baseFunElementService.saveBaseFunElement(element);
        return ResultBody.ok(result);
    }

    /**
     * 更新字段
     */
    @ApiOperation(value = "更新字段", notes = "更新功能字段元素")
    @PutMapping("/{id}")
    public ResultBody<Boolean> update(@PathVariable Long id, @Valid @RequestBody BaseFunElement element) {
        element.setId(id);
        boolean result = baseFunElementService.updateBaseFunElement(element);
        return ResultBody.ok(result);
    }

    /**
     * 删除字段
     */
    @ApiOperation(value = "删除字段", notes = "删除功能字段元素")
    @DeleteMapping("/{id}")
    public ResultBody<Boolean> delete(@PathVariable Long id) {
        boolean result = baseFunElementService.deleteBaseFunElement(id);
        return ResultBody.ok(result);
    }

//    /**
//     * 批量删除字段
//     */
//    @ApiOperation(value = "批量删除字段", notes = "批量删除功能字段元素")
//    @DeleteMapping("/batch")
//    public ResultBody<Boolean> batchDelete(@RequestBody List<Long> ids) {
//        boolean result = baseFunElementService.batchDeleteElements(ids);
//        return ResultBody.ok(result);
//    }

//    /**
//     * 检查字段英文是否存在
//     */
//    @ApiOperation(value = "检查字段英文是否存在", notes = "检查字段英文标识是否已存在")
//    @GetMapping("/check-element")
//    public ResultBody<Boolean> checkElement(@RequestParam String element,
//                                           @RequestParam Integer funType,
//                                           @RequestParam(required = false) Long id) {
//        boolean exists = baseFunElementService.checkElementExists(element, funType, id);
//        return ResultBody.ok(exists);
//    }
}
