<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="df94a903-579d-4ffd-9f16-f737eab78f46" name="更改" comment="去除多余文件，排查启动失败的原因">
      <change afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/sql/base_fun_element.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-client/src/main/java/com/aimo/base/client/model/base/BaseFunElement.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-client/src/main/java/com/aimo/base/client/model/base/BaseFunElement.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/WorkflowController.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/WorkflowController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/base/BaseFunElementController.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/base/BaseFunElementController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/base/LoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/base/LoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/message/PredictionVersionDetailAddAction.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/message/PredictionVersionDetailAddAction.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/base/impl/WorkflowServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/base/impl/WorkflowServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/custom/impl/LogisticsProviderEmailServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/custom/impl/LogisticsProviderEmailServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/utils/IPMacUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/utils/IPMacUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/mapper/custom/DeclarationBookingMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/mapper/custom/DeclarationBookingMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-erp-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-erp-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/configuration/DynamicDataSourceConfiguration.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/configuration/DynamicDataSourceConfiguration.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/controller/UserTestController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-erp-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-erp-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-erp-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-erp-server/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/AwdInventoryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/AwdInventoryController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/ListingController.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/ListingController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxAwdInventoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxAwdInventoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxFbaInventoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxFbaInventoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-client/src/main/java/com/sy/ys/client/model/base/YsProduct.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-client/src/main/java/com/sy/ys/client/model/base/YsProduct.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-server/src/main/resources/bootstrap.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30aZW4zv41E0D6Nn6j48fJh1d3v" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.aimo-base-client [clean].executor": "Run",
    "Maven.aimo-base-client [install].executor": "Run",
    "Maven.aimo-common-core [clean].executor": "Run",
    "Maven.aimo-common-core [install].executor": "Run",
    "Maven.sy-ys-client [clean].executor": "Run",
    "Maven.sy-ys-client [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.BaseApplication.executor": "Debug",
    "Spring Boot.ErpApplication.executor": "Debug",
    "Spring Boot.GatewaySpringApplication.executor": "Debug",
    "Spring Boot.LingXingApplication.executor": "Debug",
    "Spring Boot.YoungSuiteApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "run.configurations.included.in.services": "true",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.BaseApplication">
    <configuration name="BaseApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="aimo-base-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.aimo.base.server.BaseApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sy-erp-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sy.erp.server.ErpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewaySpringApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="aimo-api-spring-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.aimo.gateway.spring.server.GatewaySpringApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LingXingApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sy-lx-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sy.lingxing.server.LingXingApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YoungSuiteApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sy-ys-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sy.ys.server.YoungSuiteApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="df94a903-579d-4ffd-9f16-f737eab78f46" name="更改" comment="" />
      <created>1753867673719</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753867673719</updated>
      <workItem from="1753867675277" duration="688000" />
      <workItem from="1753868377718" duration="268000" />
      <workItem from="1753868654459" duration="13872000" />
    </task>
    <task id="LOCAL-00001" summary="提交部分工作审批流服务端代码">
      <option name="closed" value="true" />
      <created>1753870011713</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753870011713</updated>
    </task>
    <task id="LOCAL-00002" summary="提交erp模块的pom依赖文件">
      <option name="closed" value="true" />
      <created>1753923178809</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753923178809</updated>
    </task>
    <task id="LOCAL-00003" summary="提交base的pom依赖">
      <option name="closed" value="true" />
      <created>1753923656787</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753923656787</updated>
    </task>
    <task id="LOCAL-00004" summary="去除多余文件，排查启动失败的原因">
      <option name="closed" value="true" />
      <created>1753928105708</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753928105708</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="提交部分工作审批流服务端代码" />
    <MESSAGE value="提交erp模块的pom依赖文件" />
    <MESSAGE value="提交base的pom依赖" />
    <MESSAGE value="去除多余文件，排查启动失败的原因" />
    <option name="LAST_COMMIT_MESSAGE" value="去除多余文件，排查启动失败的原因" />
  </component>
</project>