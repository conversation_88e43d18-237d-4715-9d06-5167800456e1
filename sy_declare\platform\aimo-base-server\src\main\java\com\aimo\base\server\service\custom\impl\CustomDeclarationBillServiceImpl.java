package com.aimo.base.server.service.custom.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.constants.DataCodeCon;
import com.aimo.base.client.model.FileTask;
import com.aimo.base.client.model.custom.*;
import com.aimo.base.client.model.custom.stand.CustomDeclarationBillLogStand;
import com.aimo.base.client.param.custom.CustomDeclarationBillParam;
import com.aimo.base.client.param.custom.DeclarationBookingParam;
import com.aimo.base.server.controller.custom.CustomModule;
import com.aimo.base.server.mapper.custom.*;
import com.aimo.base.server.service.base.DataDictionaryService;
import com.aimo.base.server.service.base.EmailSendStatusService;
import com.aimo.base.server.service.basic.BaseCurrencyService;
import com.aimo.base.server.service.basic.FileTaskService;
import com.aimo.base.server.service.custom.CustomDeclarationBillService;
import com.aimo.base.server.service.custom.DeclarationBookingService;
import com.aimo.base.server.service.custom.LogisticsProviderService;
import com.aimo.base.server.utils.FtpUtil;
import com.aimo.common.constants.ErrorCode;
import com.aimo.common.exception.OpenAlertException;
import com.aimo.common.model.ResultBody;
import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.DateUtils;
import com.aimo.common.utils.ExcelUtil;
import com.aimo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.lang.reflect.Type;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class CustomDeclarationBillServiceImpl extends BaseServiceImpl<CustomDeclarationBillMapper, CustomDeclarationBill> implements CustomDeclarationBillService, EmailSendStatusService {
    private static final String exportRecordKey = "CUSTOM_DECLARATION_INVOICE";
    @Resource
    private CustomDeclarationBillMapper billMapper;
    @Resource
    private CustomBoxInDetailMapper boxInDetailMapper;
    @Resource
    private CustomBoxInGroupMapper boxInGroupMapper;
    @Resource
    private SaleContractMapper saleContractMapper;
    @Resource
    private AuthorizationLetterMapper authorizationLetterMapper;
    @Resource
    private BaseCurrencyService baseCurrencyService;
    @Resource
    private DeclarationBookingService declarationBookingService;
    @Resource
    private CommercialInvoiceMapper commercialInvoiceMapper;
    @Resource
    private DataDictionaryService dictionaryService;
    @Resource
    private LogisticsProviderService logisticsProviderService;
    @Resource
    private CustomXmlUtils customXmlUtils;
    @Resource
    private CustomDeclarationBillLogStandMapper billLogStandMapper;

    @Resource
    private CustomDeclarationBillExportUtils exportUtils;
    @Resource
    private FileTaskService fileTaskService;

    @Override
    public IPage<CustomDeclarationBill> listPage(CustomDeclarationBillParam param) {
        return billMapper.selectPages(new Page<>(param.getPage(), param.getLimit()), param);
    }

    public void downloadHandover(Long providerId, String picDate, HttpServletResponse response) throws Exception {
        LogisticsProvider logisticsProvider = logisticsProviderService.getLogisticsProvider(providerId, null);
        if (logisticsProvider == null) {
            throw new OpenAlertException("物流商不存在");
        }
        String whAddress = StringUtils.toMyString(logisticsProvider.getWhAddress(), StringUtils.EMPTY);
        String whMobile = StringUtils.toMyString(logisticsProvider.getWhTel(), StringUtils.EMPTY);
        String fileName = logisticsProvider.getProviderName() + "_" + picDate + "_" + "提货交接单.xls";

        QueryWrapper<CustomDeclarationBill> billQueryWrapper = new QueryWrapper<>();
        billQueryWrapper.lambda().in(CustomDeclarationBill::getPicDate, DateUtils.formatDate(DateUtils.parseDate(picDate, DateUtils.DATE_TYPE_YMD), "yyyy年MM月dd日"))
                .eq(CustomDeclarationBill::getProviderId, providerId).eq(CustomDeclarationBill::getStatus, BaseConstants.STATUS0.shortValue());
        List<CustomDeclarationBill> billList = billMapper.selectList(billQueryWrapper);
        if (CollectionUtils.isEmpty(billList)) {
            throw new OpenAlertException("请确认货代和日期,当前调价下没有可提货的合同");
        }
        List<String> customRankList = billList.stream().map(CustomDeclarationBill::getCustomRank).collect(Collectors.toList());

        QueryWrapper<DeclarationBooking> bookQueryWrapper = new QueryWrapper<>();
        bookQueryWrapper.lambda().in(DeclarationBooking::getCustomRank, customRankList).eq(DeclarationBooking::getCustomStatus, BaseConstants.STATUS1)
                .select(DeclarationBooking::getCustomRank, DeclarationBooking::getClassNames);
        List<DeclarationBooking> bookingList = declarationBookingService.list(bookQueryWrapper);

        Map<String, Set<String>> classNameSetMap = new HashMap<>();
        bookingList.forEach(item -> {
            String customRank = item.getCustomRank();
            classNameSetMap.computeIfAbsent(customRank, k -> new HashSet<>()).addAll(Arrays.asList(item.getClassNames().split(",")));
        });

        //深圳市本宜贸易有限公司  珠海市圣峪莱商贸有限公司
        Map<String, List<CustomDeclarationBill>> companyMap = new HashMap<>();
        billList.forEach(item -> companyMap.computeIfAbsent(item.getConsignorName(), k -> new ArrayList<>()).add(item));

        TemplateExportParams params = new TemplateExportParams("template/logisticsHandoverVoucher.xls");
        params.setColForEach(true);
        String mobile = StringUtils.toMyString(dictionaryService.getValueBy("finance_logistics_connector", "mobile"), StringUtils.EMPTY);//"1363265638913632656389"
        Map<String, Workbook> workbookMap = new HashMap<>();
        for (String company : companyMap.keySet()) {
            List<CustomDeclarationBill> goodsList = companyMap.get(company);
            Map<String, Object> map = new HashMap<>();
            map.put("company", company);
            map.put("logisticsName2", logisticsProvider.getProviderName());
            map.put("address", whAddress);
            map.put("mobile", mobile);//固定值，仓库人员电话,要改后续
            map.put("whMobile", whMobile);//固定值，仓库人员电话,要改后续
            map.put("logisticsName", logisticsProvider.getProviderName());
            map.put("pickDate", picDate);
            List<Map<String, Object>> dataList = new ArrayList<>();
            map.put("mapList", dataList);
            for (CustomDeclarationBill item : goodsList) {
                Map<String, Object> each = new HashMap<>();
                dataList.add(each);
                each.put("pi", item.getContractAgreementNo());
                each.put("name", StringUtils.join(classNameSetMap.get(item.getCustomRank()), ","));
                each.put("boxNum", item.getBoxQty());
                each.put("grossWeight", item.getGrossWeight());
                if (item.getVol() == null) {
                    CustomDeclarationBill bill = declarationBookingService.loadBookingInfo(null, item.getId());
                    each.put("vol", bill.getDetailList().stream().filter(detail -> detail.getVol() != null).mapToDouble(detail -> detail.getVol().setScale(2, RoundingMode.HALF_UP).doubleValue()).sum());
                } else {
                    each.put("vol", item.getVol().setScale(2, RoundingMode.HALF_UP).toString());
                }

            }
            workbookMap.put(company, ExcelExportUtil.exportExcel(params, map));
        }
        Workbook workbook;
        if (workbookMap.size() > 1) {
            List<String> companyList = new ArrayList<>(workbookMap.keySet());
            workbook = workbookMap.get(companyList.get(0));
            workbook.setSheetName(0, companyList.get(0));
            for (int i = 1; i < companyList.size(); i++) {
                Workbook otherWorkbook = workbookMap.get(companyList.get(i));
                Sheet sheet1 = workbook.createSheet(companyList.get(i));
                Sheet sheet2 = otherWorkbook.getSheetAt(0);
                ExcelUtil.copySheet(sheet2, sheet1, workbook);
            }
        } else if (workbookMap.size() == 1) {
            workbook = workbookMap.values().iterator().next();
            workbook.setSheetName(0, workbookMap.keySet().iterator().next());
        } else {
            throw new OpenAlertException("不存在需要导出的数据");
        }
        ExcelUtil.export(response, workbook, fileName);
    }


    public List<CustomDeclarationBillLogStand> getStandLog(String seqNo) {
        if (StringUtils.isEmpty(seqNo)) {
            return Lists.newArrayList();
        }
        return billLogStandMapper.getStandLog(seqNo);
    }

    public Boolean sendE(Long id) {
        try {
            CustomDeclarationBill customDeclarationBill = this.getBy(id, null);
            if (customDeclarationBill == null || BaseConstants.STATUS2.equals(customDeclarationBill.getStatusCreated())) {
                throw new OpenAlertException("报文已发送等待回执");
            }
            String xml = customXmlUtils.createXml(customDeclarationBill);
            String value = dictionaryService.getValueBy(DataCodeCon.custom_declaration_9710, "FTP_CREATE");
            if (StringUtils.isEmpty(value)) {
                throw new OpenAlertException("未配置申报保文保存ftp");
            }
            String fileName = customDeclarationBill.getSheetNo() + "_" + System.currentTimeMillis() + ".xml";//懒的去再查询了
            List<Map<String, String>> ftpSetList = JSON.parseObject(value, new TypeToken<List<Map<String, String>>>() {
            }.getType());
            if (org.springframework.util.CollectionUtils.isEmpty(ftpSetList)) {
                throw new OpenAlertException("未配置申报保文保存ftp");
            }
            String company = customDeclarationBill.getConsignorName();
            Optional<Map<String, String>> optional = ftpSetList.stream().filter(item -> StringUtils.equals(StringUtils.toMyString(item.get("company"), ""), company)).findFirst();
            if (!optional.isPresent()) {
                throw new OpenAlertException("未配置申报保文保存ftp");
            }
            Map<String, String> ftpSet = optional.get();
            customDeclarationBill.setStatusCreated(BaseConstants.STATUS2);
            customDeclarationBill.setUpdateTime(DateUtils.getCurrentDate());
            this.updateById(customDeclarationBill);
            boolean result = FtpUtil.uploadFile(ftpSet.get("ip"), StringUtils.toInteger(ftpSet.get("port")), ftpSet.get("user"), ftpSet.get("password"),
                    StringUtils.toMyString(ftpSet.get("path"), "/"), "Deccus001/OutBox", fileName, new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
            this.saveLog("报文发送", "报文上传结果：" + result, CustomModule.CUSTOM_DECLARATION_BILL, id);
            return result;
        } catch (Exception e) {
            throw new OpenAlertException(ExceptionUtils.getStackTrace(e));
        }
    }

    public Boolean resetE(Long id) {
        CustomDeclarationBill customDeclarationBill = this.getById(id);
        if (BaseConstants.STATUS2.equals(customDeclarationBill.getStatusCreated()) && DateUtils.getCurrentDate().getTime() - customDeclarationBill.getUpdateTime().getTime() <= 30 * 60 * 1000L) {
            throw new OpenAlertException("报文发送还未得到结果,请稍后再试");
        }
        customDeclarationBill.setStatusCreated(BaseConstants.STATUS0);
        customDeclarationBill.setCreatedBillNo(null);
        customDeclarationBill.setUpdateTime(DateUtils.getCurrentDate());
        this.saveLog("重置发送", "清除报文发送信息报文状态：" +
                (!StringUtils.toInteger(customDeclarationBill.getStatusCreated(), BaseConstants.STATUS0).equals(BaseConstants.STATUS0) ? "已发送" : "未发送")
                + (StringUtils.isNotEmpty(customDeclarationBill.getCreatedBillNo()) ? ("E单号" + customDeclarationBill.getCreatedBillNo()) : "未收到E单号"), CustomModule.CUSTOM_DECLARATION_BILL, id);
        return this.updateById(customDeclarationBill);
    }

    /**
     * 修改申报单位
     */
    public Boolean saveAgent(@RequestBody(required = false) CustomDeclarationBill billVo) {
        CustomDeclarationBill customDeclarationBill = billMapper.selectById(billVo.getId());
        customDeclarationBill.setAgentCode(StringUtils.trim(billVo.getAgentCode()));
        customDeclarationBill.setAgentName(StringUtils.trim(billVo.getAgentName()));
        customDeclarationBill.setUpdateTime(new Date());
        return billMapper.updateById(customDeclarationBill) > 0;
    }

    public CustomDeclarationBill getBy(Long id, String customRank) {
        if (id == null && StringUtils.isEmpty(customRank)) {
            throw new OpenAlertException("参数有误,请检查参数");
        }
        QueryWrapper<CustomDeclarationBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(id != null, CustomDeclarationBill::getId, id)
                .eq(StringUtils.isNotEmpty(customRank), CustomDeclarationBill::getCustomRank, customRank)
                .eq(CustomDeclarationBill::getStatus, BaseConstants.STATUS0.shortValue());
        CustomDeclarationBill bill = billMapper.selectOne(queryWrapper);
        if (bill == null) {
            return null;
        }
        bill.setDetailList(selectDetailList(bill.getId()));
        bill.setGroupList(selectGroupIst(bill.getId()));
        bill.setSaleContract(selectSaleContract(bill.getId()));
        bill.setAuthorizationLetter(selectAuthorizationLetter(bill.getId()));
        bill.setInvoice(selectCommercialInvoice(bill.getId()));
        Long currencyId = bill.getGroupList().get(0).getCurrency();
        if (currencyId != null) {
            bill.setCurrency(baseCurrencyService.getById(currencyId));
        }
        return bill;
    }

    private CommercialInvoice selectCommercialInvoice(Long id) {
        QueryWrapper<CommercialInvoice> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(id != null, CommercialInvoice::getParentId, id);
        return commercialInvoiceMapper.selectOne(queryWrapper);
    }

    public List<CustomBoxInDetail> selectDetailList(Long billId) {
        QueryWrapper<CustomBoxInDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(billId != null, CustomBoxInDetail::getParentId, billId);
        return boxInDetailMapper.selectList(queryWrapper);
    }

    public List<CustomBoxInGroup> selectGroupIst(Long billId) {
        QueryWrapper<CustomBoxInGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(billId != null, CustomBoxInGroup::getParentId, billId);
        List<CustomBoxInGroup> groupList = boxInGroupMapper.selectList(queryWrapper);
        groupList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDeclarationElement())) {
                item.setDeclarationElementList(JSON.parseArray(item.getDeclarationElement(), DeclarationElement.class));
            } else {
                item.setDeclarationElementList(new ArrayList<>());
            }
        });
        return groupList;
    }

    public SaleContract selectSaleContract(Long billId) {
        QueryWrapper<SaleContract> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(billId != null, SaleContract::getParentId, billId);
        return saleContractMapper.selectOne(queryWrapper);
    }

    public AuthorizationLetter selectAuthorizationLetter(Long billId) {
        QueryWrapper<AuthorizationLetter> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(billId != null, AuthorizationLetter::getParentId, billId);
        return authorizationLetterMapper.selectOne(queryWrapper);
    }

    public Long addCustomDeclarationBill(CustomDeclarationBill bill) {
        QueryWrapper<CustomDeclarationBill> billQueryWrapper = new QueryWrapper<>();
        billQueryWrapper.lambda().eq(CustomDeclarationBill::getCustomRank, bill.getCustomRank()).eq(CustomDeclarationBill::getStatus, BaseConstants.STATUS0.shortValue());
        List<CustomDeclarationBill> billList = billMapper.selectList(billQueryWrapper);
        if(!CollectionUtils.isEmpty(billList)){
            throw new OpenAlertException("报关数据已经生成,请刷新界面数据再操作");
        }
        billMapper.insert(bill);
        bill.getDetailList().stream().peek(item -> item.setParentId(bill.getId())).forEach(item -> boxInDetailMapper.insert(item));
        bill.getGroupList().stream().peek(item -> item.setParentId(bill.getId())).forEach(item -> {
            if (!CollectionUtils.isEmpty(item.getDeclarationElementList())) {
                item.setDeclarationElement(JSON.toJSONString(item.getDeclarationElementList()));
            }
            boxInGroupMapper.insert(item);
        });
        bill.getSaleContract().setParentId(bill.getId());
        saleContractMapper.insert(bill.getSaleContract());
        bill.getAuthorizationLetter().setParentId(bill.getId());
        authorizationLetterMapper.insert(bill.getAuthorizationLetter());
        bill.getInvoice().setParentId(bill.getId());
        commercialInvoiceMapper.insert(bill.getInvoice());
        UpdateWrapper<DeclarationBooking> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(DeclarationBooking::getCustomStatus, BaseConstants.STATUS1).eq(DeclarationBooking::getCustomRank, bill.getCustomRank())
                .eq(DeclarationBooking::getStatus, BaseConstants.STATUS2);
        declarationBookingService.update(null, updateWrapper);
        return bill.getId();
    }

    public Boolean delCustomDeclarationBill(Long id) {
        if (id == null) {
            throw new OpenAlertException("参数有问题");
        }
        CustomDeclarationBill bill = billMapper.selectById(id);
        bill.setStatus(BaseConstants.STATUS1);
        boolean result = billMapper.updateById(bill) > 0;
        if (result) {
            UpdateWrapper<DeclarationBooking> bookingWrapper = new UpdateWrapper<>();
            bookingWrapper.lambda().set(DeclarationBooking::getCustomStatus, BaseConstants.STATUS0)
                    .eq(DeclarationBooking::getCustomRank, bill.getCustomRank())
                    .eq(DeclarationBooking::getStatus, BaseConstants.STATUS2);
            declarationBookingService.update(null, bookingWrapper);
        }
        return result;
    }

    @Override
    public boolean updateSendStatus(List<Long> ids) {
        UpdateWrapper<CustomDeclarationBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(CustomDeclarationBill::getEmailStatus, BaseConstants.STATUS1.shortValue())
                .set(CustomDeclarationBill::getEmailDate, DateUtils.getCurrentDate())
                .in(CustomDeclarationBill::getId, ids);
        return billMapper.update(null, updateWrapper) > 0;
    }


    @Override
    public ResultBody<Long> exportFile(CustomDeclarationBillParam param) {
        FileTask fileTask = new FileTask();
        fileTask.setTaskKey(exportRecordKey);
        fileTask.setTaskType(FileTask.TASK_TYPE_EXPORT);
        fileTask.setCount(0);
        fileTask.setStatus(0);
        fileTask.setParams(JSON.toJSONString(param));
        fileTask.setCreateUser(OpenHelper.getUserId());
        ResultBody<Long> resultBody = fileTaskService.addTask(fileTask);
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (resultBody.isOk() && resultBody.getData() != null) {
            fileTask.setId(resultBody.getData());
            Thread thread = new Thread(() -> exportProcess(fileTask,requestAttributes));
            thread.start();
            return ResultBody.ok(resultBody.getData());
        } else {
            return ResultBody.failed(null, resultBody.getCode(), resultBody.getMessage());
        }
    }
    private void exportProcess(FileTask fileTask, RequestAttributes requestAttributes) {
        fileTask.setStatus(FileTask.STATUS_RUNNING);
        fileTaskService.updateTask(fileTask);
        try{
            Type type = new TypeToken<CustomDeclarationBillParam>() {
            }.getType();
            CustomDeclarationBillParam param = JSON.parseObject(fileTask.getParams(), type);
            param.setPage(1);
            param.setLimit(-1);

            IPage<CustomDeclarationBill> iPage = this.listPage(param);
            if (CollectionUtils.isEmpty(iPage.getRecords())) {
                throw new OpenAlertException("没有数据,请确认");
            }
            QueryWrapper<CustomBoxInGroup> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(CustomBoxInGroup::getParentId,iPage.getRecords().stream().map(CustomDeclarationBill::getId).collect(Collectors.toList()));
            Map<Long,List<CustomBoxInGroup>> groupListMap =new HashMap<>();
            boxInGroupMapper.selectList(queryWrapper).forEach(item->groupListMap.computeIfAbsent(item.getParentId(),k->new ArrayList<>()).add(item));
            iPage.getRecords().forEach(item->{
                item.setGroupList(groupListMap.get(item.getId()));
            });
            RequestContextHolder.setRequestAttributes(requestAttributes);
            Workbook workbook = exportUtils.exportData(iPage.getRecords());
            String filePath = this.updateFilePath(workbook,2);
            if (StringUtils.isNotEmpty(filePath)) {
                fileTask.setFilePath(filePath);
                fileTask.setRemark(null);
                fileTask.setStatus(FileTask.STATUS_COMPLETED);
            } else {
                fileTask.setRemark("保存文件失败");
                fileTask.setStatus(FileTask.STATUS_ERROR);
            }
            fileTaskService.updateTask(fileTask);
        }catch (Exception e){
            fileTask.setRemark(StringUtils.substring(e.getMessage()+","+org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(e), 0, StringUtils.ERROR_LENGTH));
            fileTask.setStatus(FileTask.STATUS_ERROR);
            fileTaskService.updateTask(fileTask);
        }
    }

    @Override
    public ResultBody<String> execute(Long id) {
        ResultBody<FileTask> resultBody = fileTaskService.executeCheck(id, exportRecordKey, FileTask.TASK_TYPE_EXPORT);
        if (!resultBody.isOk()) {
            return ResultBody.failed(null, resultBody.getCode(), resultBody.getMessage());
        }
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        new Thread(() -> exportProcess(resultBody.getData(),requestAttributes)).start();
        return ResultBody.ok(ErrorCode.OK.getMessage());
    }
}
