<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aimo.base.server.mapper.prediction.PredictionStockLockMapper">
    <resultMap id="stockLockMap" type="com.aimo.base.client.model.prediction.PredictionStockLock">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="productId" column="product_id"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productSpec" column="product_spec"/>
        <result property="lockQty" column="lock_qty"/>
        <result property="qty" column="qty"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="ysMessage" column="ys_message"/>
        <result property="ysId" column="ys_id"/>
        <result property="createUserName" column="create_user_name"/>
        <association property="versionAsin" javaType="com.aimo.base.client.model.prediction.PredictionVersionAsin" resultMap="com.aimo.base.server.mapper.prediction.PredictionMonthPurchaseMapper.versionAsinMap">
        </association>
    </resultMap>

    <select id="selectPages" resultMap="stockLockMap" parameterType="com.aimo.base.client.param.salePrediction.PredictionStockLockParam">
        select o.*,f.code as 'product_code',f.name as 'product_name',f.spec as 'product_spec',
               o.parent_id as 'parent_asin_id',a.color,a.size,a.parent_id as 'parent_asin_parent_id',a.prediction_id,
               b.id as 'sale_prediction_id' ,b.shop_id,c.name as shop_name,b.listing_title,b.spec_type,b.spu,
               d.nick_name as create_user_name,e.nick_name as update_user_name
        from bussiness_prediction_stock_lock o
        left join base_product f on f.id = o.product_id
        left join bussiness_prediction_version_asin a on o.parent_id = a.id
        left join bussiness_sale_prediction b on a.prediction_id = b.id
        left join base_shop c on b.shop_id = c.id
        left join base_user d on o.create_user = d.id
        left join base_user e on o.update_user = e.id
        <where>
            o.status = 0 and ifnull(a.status,0) = 0
            <if test="param.createUser != null">
                and o.create_user =  #{param.createUser}
            </if>
            <if test="param.color != null and param.color != ''">
                and a.color = #{param.color}
            </if>
            <if test="param.size != null and param.size != ''">
                and a.size = #{param.size}
            </if>
            <if test="param.predictionIdList!= null and param.predictionIdList.size>0">
                <foreach separator="," item="predictionId" open=" and a.prediction_id in(" close=")" collection="param.predictionIdList">
                    #{predictionId}
                </foreach>
            </if>
            <if test="param.productCodeList!= null and param.productCodeList.size>0">
                <foreach separator="," item="productCode" open=" and f.code in(" close=")" collection="param.productCodeList">
                    #{productCode}
                </foreach>
            </if>
            <if test="param.remark != null and param.remark != ''">
                and o.remark like CONCAT('%',#{param.remark},'%')
            </if>
            <if test="param.startDate != null and param.startDate!=''">
                and o.create_time >= #{param.startDate}
            </if>
            <if test="param.endDate != null and param.endDate!=''">
                and o.create_time &lt;= #{param.endDate}
            </if>
        </where>
        order by o.id DESC
    </select>
</mapper>
