package com.aimo.base.server.message;

import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.model.basf.BaseProduct;
import com.aimo.base.client.model.newApply.ListingOrm;
import com.aimo.base.client.model.prediction.*;
import com.aimo.base.client.param.salePrediction.SalePredictionAsinDetailParam;
import com.aimo.base.server.mapper.prediction.PredictionVersionAsinMapper;
import com.aimo.base.server.mapper.prediction.PredictionVersionDetailMapper;
import com.aimo.base.server.mapper.prediction.SalePredictionTimeMapper;
import com.aimo.base.server.service.basf.BaseProductService;
import com.aimo.base.server.service.newApply.ListingOrmService;
import com.aimo.base.server.service.prediction.PredictionSendSplitStandardService;
import com.aimo.base.server.service.prediction.SalePredictionAsinDetailService;
import com.aimo.base.server.service.prediction.SalePredictionService;
import com.aimo.common.exception.OpenAlertException;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class PredictionVersionDetailAddAction implements MessageAction {
    @Resource
    private PredictionVersionDetailMapper versionDetailMapper;
    @Resource
    private SalePredictionService salePredictionService;

    @Resource
    private SalePredictionAsinDetailService asinDetailService;
    @Resource
    private SalePredictionTimeMapper salePredictionTimeMapper;
    @Resource
    private PredictionVersionAsinMapper versionAsinMapper;
    @Resource
    private ListingOrmService ormService;
    @Resource
    private PredictionSendSplitStandardService sendSplitStandardService;
    @Resource
    private BaseProductService baseProductService;

    @Override
    public boolean execute(Long userId, String message) {
        try {
            Map<String, Object> param = this.getMessage(message);
            Long timeId = StringUtils.toLong(param.get("id"));
            if (timeId == null) {
                throw new OpenAlertException("参数有误");
            }
            SalePredictionTime saleTime = salePredictionService.getSalePredictionTime(timeId);
            String monthKey = StringUtils.toMyString(param.get("monthKey"));
            if (StringUtils.isNotEmpty(monthKey)) {
                saleTime.setDetailList(saleTime.getDetailList().stream().filter(item -> monthKey.contains(item.getMonthKey())).collect(Collectors.toList()));
            }
            if (saleTime == null || CollectionUtils.isEmpty(saleTime.getDetailList()) || saleTime.getSalePrediction() == null) {
                throw new OpenAlertException("导入数据有误" + timeId);
            }
            if (saleTime.getRunStatus() != -1) {
                throw new OpenAlertException("数据状态有误,未提交运行" + timeId);
            }
            List<String> monthKeyList = saleTime.getDetailList().stream().map(SalePredictionDetail::getMonthKey).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(monthKeyList)) {
                throw new OpenAlertException("数据内容有误,未有月份预测" + timeId);
            }
            QueryWrapper<PredictionVersionDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(PredictionVersionDetail::getTimeId, timeId).in(PredictionVersionDetail::getMonthKey, monthKeyList);
            List<PredictionVersionDetail> versionDetailList = versionDetailMapper.selectList(detailQueryWrapper);
            versionDetailList.clear();
            if (!CollectionUtils.isEmpty(versionDetailList)) {
                throw new OpenAlertException("数据已经处理,请核实后再试" + timeId);
            }

            QueryWrapper<SalePredictionTime> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SalePredictionTime::getParentId, saleTime.getParentId()).lt(SalePredictionTime::getId, timeId)
                    .eq(SalePredictionTime::getStatus, BaseConstants.STATUS0).ne(SalePredictionTime::getRunStatus, BaseConstants.STATUS1);
            List<SalePredictionTime> noProcessList = salePredictionTimeMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(noProcessList)) {
                throw new OpenAlertException(BaseConstants.MESSAGE_RETRY + "存在已失败的记录导致本次记录无法处理" + timeId + ";失败的记录ID：" + StringUtils.toLongArray(noProcessList.stream().map(SalePredictionTime::getId).collect(Collectors.toList()), StringUtils.commaSp));
            }
            monthKeyList.sort(String::compareTo);
            SalePredictionAsinDetailParam versionParam = new SalePredictionAsinDetailParam();
            versionParam.setPredictionIds(StringUtils.toMyString(saleTime.getParentId()));
            versionParam.setParentIds(StringUtils.toMyString(PredictionVersion.currentVersionId));
            versionParam.setLimit(-1);
            Map<String, PredictionVersionAsin> asinMap = asinDetailService.listPage(versionParam).getRecords().stream().collect(Collectors.toMap(item -> StringUtils.concat(StringUtils.specialSp, item.getColor(), item.getSize()), item -> item, (o1, o2) -> o1));
            Date currentDate = saleTime.getCreateTime();
            SalePrediction salePrediction = saleTime.getSalePrediction();
            Map<String, ListingOrm> ormMap = ormService.listListingOrm(salePrediction.getShopId(), salePrediction.getListingTitle());
            List<PredictionVersionAsin> versionAsinList = new ArrayList<>();
            if (CollectionUtils.isEmpty(saleTime.getRtoList())) {
                versionAsinList.addAll(asinMap.values());
            } else {
                saleTime.getRtoList().forEach(rto -> {
                    String key = rto.getKey();
                    PredictionVersionAsin versionAsin = asinMap.remove(key);
                    if (versionAsin == null) {
                        versionAsin = new PredictionVersionAsin(saleTime.getParentId(), rto.getColor(), rto.getSize());
                    }
                    setOrm(rto, versionAsin, ormMap, salePrediction);
                    if (versionAsin.getProductId() == null) {
                        QueryWrapper<BaseProduct> productQueryWrapper = new QueryWrapper<>();
                        productQueryWrapper.lambda().eq(BaseProduct::getName, salePrediction.getSpu()).eq(BaseProduct::getColor, rto.getColor())
                                .eq(BaseProduct::getSize, rto.getSize())
                                .isNull(StringUtils.isEmpty(salePrediction.getInseam()), BaseProduct::getInseam)
                                .eq(StringUtils.isNotEmpty(salePrediction.getInseam()), BaseProduct::getInseam, salePrediction.getInseam());
                        List<BaseProduct> baseProductList = baseProductService.list(productQueryWrapper);
                        if (CollectionUtils.isEmpty(baseProductList)) {
                            throw new OpenAlertException("型号" + salePrediction.getSpu() + ":内长" + salePrediction.getInseam()
                                    + ":颜色" + rto.getColor() + ":尺码" + rto.getSize() + "不存在物料");
                        }
                        versionAsin.setProductId(baseProductList.get(0).getId());
                    }
                    if (rto.getSaleRate() != null) {
                        versionAsin.setSaleRate(rto.getSaleRate());
                    } else {
                        versionAsin.setColorRate(rto.getColorRate());
                        versionAsin.setSizeRate(rto.getSizeRate());
                    }
                    versionAsinList.add(versionAsin);
                });
                if (!asinMap.isEmpty()) {
                    asinMap.values().forEach(item -> {
                        item.setSaleRate(BigDecimal.ZERO);
                        item.setStatus(BaseConstants.STATUS1);
                        versionAsinList.add(item);
                    });
                }
            }
            versionAsinList.sort(Comparator.comparing(PredictionVersionAsin::getSaleRate));
            saleTime.getDetailList().forEach(monthDetail -> {
                BigDecimal saleQty = monthDetail.getSaleQty();
                BigDecimal sumQty = BigDecimal.ZERO;
                PredictionVersionDetail versionDetail = null;
                for (PredictionVersionAsin versionAsin : versionAsinList) {
                    versionDetail = new PredictionVersionDetail();
                    versionDetail.setMonthKey(monthDetail.getMonthKey());
                    versionDetail.setTimeId(saleTime.getId());
                    versionDetail.setColorRate(versionAsin.getColorRate());
                    versionDetail.setSizeRate(versionAsin.getSizeRate());
                    versionDetail.setSaleRate(versionAsin.getSaleRate());
                    List<PredictionVersionDetail> detailList = versionAsin.getDetailList();
                    if (detailList == null) {
                        detailList = new ArrayList<>();
                        versionAsin.setDetailList(detailList);
                    }
                    if (versionAsin.getColorRate() != null) {
                        versionDetail.setColorRate(versionAsin.getColorRate());
                    }
                    if (versionAsin.getSizeRate() != null) {
                        versionDetail.setSizeRate(versionAsin.getSizeRate());
                    }
                    versionDetail.setSaleRate(versionAsin.getSaleRate());
                    versionDetail.setReturnRate(salePrediction.getReturnRate());
                    versionDetail.setGoodRate(salePrediction.getGoodRate());
                    versionDetail.setSaleQty(monthDetail.getSaleQty().multiply(StringUtils.toBigDecimal(versionAsin.getSaleRate(), BigDecimal.ZERO)).setScale(BaseConstants.DECIMAL_SCALE_2, RoundingMode.HALF_UP));
                    detailList.add(versionDetail);
                    sumQty = sumQty.add(versionDetail.getSaleQty());
                }
                if (versionDetail != null) {
                    BigDecimal difQty = sumQty.subtract(saleQty);
                    versionDetail.setSaleQty(versionDetail.getSaleQty().subtract(difQty));
                }
            });
            versionAsinList.forEach(versionAsin -> {
                if (versionAsin.getId() != null) {
                    versionAsinMapper.updateById(versionAsin);
                } else {
                    versionAsin.setCreateTime(currentDate);
                    versionAsinMapper.insert(versionAsin);
                    PredictionSendSplitStandard splitStandard = new PredictionSendSplitStandard(versionAsin.getId());
                    splitStandard.setUpdateUser(userId);
                    splitStandard.setCreateUser(userId);
                    sendSplitStandardService.save(splitStandard);
                }
                versionAsin.getDetailList().stream().peek(detail -> detail.setParentId(versionAsin.getId())).forEach(detail -> {
                    if (detail.getId() != null) {
                        detail.setStatus(BaseConstants.STATUS1);
                        detail.setUpdateTime(currentDate);
                        versionDetailMapper.updateById(detail);
                    } else {
                        detail.setCreateTime(currentDate);
                        detail.setUpdateTime(currentDate);
                        versionDetailMapper.insert(detail);

                    }
                });
            });
            UpdateWrapper<SalePredictionTime> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SalePredictionTime::getId, timeId).set(SalePredictionTime::getRunStatus, BaseConstants.STATUS1);
            return salePredictionTimeMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            if (e instanceof OpenAlertException) {
                throw (OpenAlertException) e;
            } else {
                throw new OpenAlertException(e.getMessage());
            }
        }
    }

    private void setOrm(SalePredictionRto rto, PredictionVersionAsin versionAsin, Map<String, ListingOrm> ormMap, SalePrediction salePrediction) {
        if (versionAsin.getOrmId() == null) {
            ListingOrm orm = ormMap.get(StringUtils.concat(",", Arrays.asList(salePrediction.getSpu(), StringUtils.toMyString(salePrediction.getInseam(), StringUtils.emptyStr),
                    rto.getColor(), rto.getSize())));
            if (orm != null) {
                versionAsin.setOrmId(orm.getId());
                versionAsin.setProductId(orm.getProductId());
            }
        }
    }
}
