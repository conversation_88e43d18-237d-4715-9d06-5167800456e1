package com.sy.ys.server.controller.thdOut;

import com.aimo.common.constants.ErrorCode;
import com.aimo.common.model.ResultBody;
import com.sy.ys.client.model.othOrder.YsOthOutOrder;
import com.sy.ys.client.param.othOrder.YsOthOutOrderParam;
import com.sy.ys.client.service.IYsOthOutOrderClient;
import com.sy.ys.server.sync.othOrder.OthOutOrderSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;

@Api(value = "委外订单", tags = "委外订单")
@RestController
@RequestMapping(value = "/othOutOrder")
public class OthOutOrderController implements IYsOthOutOrderClient {
    @Resource
    private OthOutOrderSyncService othOutOrderSyncService;

    @ApiOperation(value = "新增其他出库单", notes = "新增其他出库单")
    @PostMapping("/addOthOutOrder")
    @ResponseBody
    public ResultBody<Long> addOthOutOrder(@RequestBody YsOthOutOrderParam ysOthOutOrderParam) {
        try {
            return ResultBody.ok(othOutOrderSyncService.addYsOthOutOrder(ysOthOutOrderParam));
        } catch (Exception e) {
            return ResultBody.failed(-1L, ErrorCode.FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取其他出库单", notes = "获取其他出库单")
    @PostMapping("/getOthOutOrder")
    @ResponseBody
    public ResultBody<YsOthOutOrder> getOthOutOrder(@RequestParam(value = "id") Long id) {
        try {
            return ResultBody.ok(othOutOrderSyncService.getOthOutOrder(id));
        } catch (Exception e) {
            return ResultBody.failed(null, ErrorCode.FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "删除其他出库单", notes = "删除其他出库单")
    @PostMapping("/delOthOutOrder")
    @ResponseBody
    public ResultBody<Boolean> delOthOutOrder(@RequestParam(value = "id") Long id) {
        try {
            return ResultBody.ok(othOutOrderSyncService.delYsOthOutOrder(Collections.singletonList(id)));
        } catch (Exception e) {
            return ResultBody.failed(false, ErrorCode.FAIL.getCode(), e.getMessage());
        }
    }
}
