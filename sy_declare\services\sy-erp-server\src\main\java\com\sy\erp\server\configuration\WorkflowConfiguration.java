package com.sy.erp.server.configuration;

import com.sy.erp.server.service.WorkflowService;
import com.sy.erp.server.service.impl.WorkflowFallbackServiceImpl;
import com.sy.erp.server.service.impl.WorkflowServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 工作流配置类
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@Configuration
public class WorkflowConfiguration {

    @Autowired(required = false)
    @Qualifier("workflowSafeProcessEngine")
    private ProcessEngine processEngine;

    @Bean
    @Primary
    @ConditionalOnMissingBean(WorkflowService.class)
    public WorkflowService workflowService() {
        if (processEngine != null) {
            log.info("工作流引擎可用，使用完整的WorkflowServiceImpl");
            return new WorkflowServiceImpl();
        } else {
            log.warn("工作流引擎不可用，使用降级的WorkflowFallbackServiceImpl");
            return new WorkflowFallbackServiceImpl();
        }
    }
}
