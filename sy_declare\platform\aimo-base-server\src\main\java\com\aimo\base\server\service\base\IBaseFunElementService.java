package com.aimo.base.server.service.base;

import com.aimo.base.client.model.base.BaseFunElement;
import com.aimo.common.mybatis.base.service.IBaseService;
import com.aimo.common.model.PageParams;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 基础功能元素服务接口
 *
 * <AUTHOR>
 */
public interface IBaseFunElementService extends IBaseService<BaseFunElement> {
    
    /**
     * 分页查询基础功能元素
     *
     * @param pageParams 分页参数
     * @return 分页结果
     */
    IPage<BaseFunElement> listPage(PageParams<BaseFunElement> pageParams);
    
    /**
     * 根据功能类型查询基础功能元素
     *
     * @param funType 功能类型
     * @return 基础功能元素列表
     */
    List<BaseFunElement> getByFunType(Integer funType);
    
    /**
     * 根据功能类型和类型查询基础功能元素
     *
     * @param funType 功能类型
     * @param type 类型
     * @return 基础功能元素列表
     */
    List<BaseFunElement> getByFunTypeAndType(Integer funType, Integer type);
    
    /**
     * 根据元素英文名查询基础功能元素
     *
     * @param element 元素英文名
     * @return 基础功能元素
     */
    BaseFunElement getByElement(String element);
    
    /**
     * 检查元素英文名是否存在
     *
     * @param element 元素英文名
     * @return 是否存在
     */
    boolean checkElement(String element);
    
    /**
     * 保存基础功能元素
     *
     * @param baseFunElement 基础功能元素
     * @return 是否成功
     */
    boolean saveBaseFunElement(BaseFunElement baseFunElement);
    
    /**
     * 更新基础功能元素
     *
     * @param baseFunElement 基础功能元素
     * @return 是否成功
     */
    boolean updateBaseFunElement(BaseFunElement baseFunElement);
    
    /**
     * 删除基础功能元素
     *
     * @param id 基础功能元素ID
     * @return 是否成功
     */
    boolean deleteBaseFunElement(Long id);
    
    /**
     * 获取所有正常状态的基础功能元素
     *
     * @return 基础功能元素列表
     */
    List<BaseFunElement> getAllNormalElements();
}
