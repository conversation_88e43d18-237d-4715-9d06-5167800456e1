package com.aimo.base.server.service.base.impl;

import com.aimo.base.client.model.base.WorkflowFunctionType;
import com.aimo.base.server.mapper.base.WorkflowFunctionTypeMapper;
import com.aimo.base.server.service.base.WorkflowFunctionTypeService;
import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.model.PageParams;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工作流功能类型服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkflowFunctionTypeServiceImpl extends BaseServiceImpl<WorkflowFunctionTypeMapper, WorkflowFunctionType> implements WorkflowFunctionTypeService {

    @Resource
    private WorkflowFunctionTypeMapper workflowFunctionTypeMapper;

    @Override
    public IPage<WorkflowFunctionType> listPage(PageParams<WorkflowFunctionType> pageParams) {
        WorkflowFunctionType query = pageParams.mapToObject(WorkflowFunctionType.class);
        QueryWrapper<WorkflowFunctionType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .like(StringUtils.isNotEmpty(query.getName()), WorkflowFunctionType::getName, query.getName())
                .like(StringUtils.isNotEmpty(query.getKey()), WorkflowFunctionType::getKey, query.getKey())
                .eq(WorkflowFunctionType::getStatus, 0); // 只查询正常状态的记录
        queryWrapper.orderByDesc("create_time");
        return workflowFunctionTypeMapper.selectPage(pageParams, queryWrapper);
    }

    @Override
    public WorkflowFunctionType getByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        QueryWrapper<WorkflowFunctionType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WorkflowFunctionType::getKey, key)
                .eq(WorkflowFunctionType::getStatus, 0);
        return workflowFunctionTypeMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean checkKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        QueryWrapper<WorkflowFunctionType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WorkflowFunctionType::getKey, key)
                .eq(WorkflowFunctionType::getStatus, 0);
        return workflowFunctionTypeMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean saveWorkflowFunctionType(WorkflowFunctionType workflowFunctionType) {
        if (workflowFunctionType == null) {
            return false;
        }
        workflowFunctionType.setCreateTime(new Date());
        workflowFunctionType.setCreateUser(OpenHelper.getUserId());
        workflowFunctionType.setUpdateTime(workflowFunctionType.getCreateTime());
        workflowFunctionType.setUpdateUser(workflowFunctionType.getCreateUser());
        workflowFunctionType.setStatus(0); // 正常状态
        return workflowFunctionTypeMapper.insert(workflowFunctionType) > 0;
    }

    @Override
    public boolean updateWorkflowFunctionType(WorkflowFunctionType workflowFunctionType) {
        if (workflowFunctionType == null || workflowFunctionType.getId() == null) {
            return false;
        }
        workflowFunctionType.setUpdateTime(new Date());
        workflowFunctionType.setUpdateUser(OpenHelper.getUserId());
        return workflowFunctionTypeMapper.updateById(workflowFunctionType) > 0;
    }

    @Override
    public boolean deleteWorkflowFunctionType(Long id) {
        if (id == null) {
            return false;
        }
        WorkflowFunctionType workflowFunctionType = new WorkflowFunctionType();
        workflowFunctionType.setId(id);
        workflowFunctionType.setStatus(1); // 删除状态
        workflowFunctionType.setUpdateTime(new Date());
        workflowFunctionType.setUpdateUser(OpenHelper.getUserId());
        return workflowFunctionTypeMapper.updateById(workflowFunctionType) > 0;
    }

    @Override
    public List<WorkflowFunctionType> getAllNormalTypes() {
        QueryWrapper<WorkflowFunctionType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WorkflowFunctionType::getStatus, 0);
        queryWrapper.orderByDesc("create_time");
        return workflowFunctionTypeMapper.selectList(queryWrapper);
    }
}
