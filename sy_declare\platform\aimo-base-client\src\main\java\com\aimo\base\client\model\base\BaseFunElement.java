package com.aimo.base.client.model.base;

import com.aimo.common.mybatis.base.entity.UpdateEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 功能字段元素实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("base_fun_element")
public class BaseFunElement extends UpdateEntity<BaseFunElement> {

    /**
     * 字段名称
     */
    @TableField("name")
    private String name;

    /**
     * 字段英文
     */
    @TableField("element")
    private String element;

    /**
     * 功能枚举值：上新功能10001
     */
    @TableField("fun_type")
    private Integer funType;

    /**
     * 类型 1：基础信息 2：标准信息
     */
    @TableField("type")
    private Integer type;

    /**
     * 状态 0：正常 1：删除
     */
    @TableField(value = "`status`")
    private Integer status;
}
