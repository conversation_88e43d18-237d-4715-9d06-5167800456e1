package com.sy.erp.server.service.impl;

import com.sy.erp.server.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流降级服务实现类 - 当工作流引擎未初始化时使用
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@Service
@ConditionalOnMissingBean(name = "workflowServiceImpl")
public class WorkflowFallbackServiceImpl implements WorkflowService {

    private static final String DISABLED_MESSAGE = "工作流引擎未初始化，可能是配置未加载或数据库连接失败";

    @Override
    public Map<String, Object> deployProcess(String processName, String processKey, String bpmnXml) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> deleteDeployment(String deploymentId, boolean cascade) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> startProcess(String processDefinitionKey, String businessKey, Map<String, Object> variables) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> completeTask(String taskId, Map<String, Object> variables) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getProcessDefinitions(Pageable pageable) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getProcessInstances(Pageable pageable) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getUserTasks(String userId, Pageable pageable) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getAssignedTasks(String userId, Pageable pageable) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getCandidateTasks(String userId, Pageable pageable) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getProcessDiagram(String processInstanceId) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getProcessHistory(String processInstanceId) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> suspendProcessDefinition(String processDefinitionId) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> activateProcessDefinition(String processDefinitionId) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> getProcessVariables(String processInstanceId) {
        return createDisabledResponse();
    }

    @Override
    public Map<String, Object> setProcessVariables(String processInstanceId, Map<String, Object> variables) {
        return createDisabledResponse();
    }

    private Map<String, Object> createDisabledResponse() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", DISABLED_MESSAGE);
        result.put("code", "WORKFLOW_ENGINE_NOT_INITIALIZED");
        result.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> help = new HashMap<>();
        help.put("checkNacos", "检查Nacos配置中心的workflow_properties配置");
        help.put("checkDatabase", "确保数据库连接正常");
        help.put("checkLogs", "查看应用启动日志中的工作流初始化信息");
        result.put("help", help);
        
        return result;
    }
}
