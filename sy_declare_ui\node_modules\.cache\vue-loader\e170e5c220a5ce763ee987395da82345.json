{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue?vue&type=style&index=0&id=26384284&lang=less&scoped=true&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue", "mtime": 1753933128519}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci53b3JrZmxvdy1saXN0IHsKICBwYWRkaW5nOiAxNnB4OwoKICAuc2VhcmNoLWZvcm0gewogICAgbWFyZ2luLWJvdHRvbTogMTZweDsKCiAgICAuaXZ1LWZvcm0taXRlbSB7CiAgICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICAgIG1hcmdpbi1yaWdodDogMTZweDsKICAgIH0KICB9CgogIC5hY3Rpb24tYnV0dG9ucyB7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDhweDsKCiAgICAucmlnaHQtYnV0dG9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGdhcDogOHB4OwogICAgfQogIH0KCiAgLnBhZ2luYXRpb24td3JhcHBlciB7CiAgICBtYXJnaW4tdG9wOiAxNnB4OwogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgfQoKICAud29ya2Zsb3ctdmlldyB7CiAgICAud29ya2Zsb3ctaW5mbyB7CiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7CgogICAgICAuaW5mby1pdGVtIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAgICAgLmluZm8tbGFiZWwgewogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgICAgIGNvbG9yOiAjNTE1YTZlOwogICAgICAgICAgbWluLXdpZHRoOiA4MHB4OwogICAgICAgIH0KCiAgICAgICAgLmluZm8tdmFsdWUgewogICAgICAgICAgY29sb3I6ICMxNzIzM2Q7CiAgICAgICAgfQogICAgICB9CiAgICB9CgogICAgLndvcmtmbG93LXByZXZpZXcgewogICAgICAucHJldmlldy1jYW52YXMgewogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICBoZWlnaHQ6IDQwMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U4ZWFlYzsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgICAgICAgLnByZXZpZXctbm9kZSB7CiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgICB3aWR0aDogNTBweDsKICAgICAgICAgIGhlaWdodDogMjVweDsKICAgICAgICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RjZGVlMjsKICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgICAgZm9udC1zaXplOiAxMHB4OwogICAgICAgICAgdXNlci1zZWxlY3Q6IG5vbmU7CgogICAgICAgICAgJi5ub2RlLXN0YXJ0IHsKICAgICAgICAgICAgYmFja2dyb3VuZDogIzUyYzQxYTsKICAgICAgICAgICAgY29sb3I6IHdoaXRlOwogICAgICAgICAgICBib3JkZXItY29sb3I6ICM1MmM0MWE7CiAgICAgICAgICB9CgogICAgICAgICAgJi5ub2RlLWVuZCB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmNTIyMmQ7CiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsKICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjZjUyMjJkOwogICAgICAgICAgfQoKICAgICAgICAgICYubm9kZS1hcHByb3ZhbCB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsKICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICAgICAgfQoKICAgICAgICAgICYubm9kZS1jb25kaXRpb24gewogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmE4YzE2OwogICAgICAgICAgICBjb2xvcjogd2hpdGU7CiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2ZhOGMxNjsKICAgICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoNDVkZWcpOwoKICAgICAgICAgICAgLm5vZGUtdGl0bGUgewogICAgICAgICAgICAgIHRyYW5zZm9ybTogcm90YXRlKC00NWRlZyk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAmLm5vZGUtdGFzayB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmMGYwZjA7CiAgICAgICAgICAgIGNvbG9yOiAjMzMzOwogICAgICAgICAgfQoKICAgICAgICAgIC5ub2RlLXRpdGxlIHsKICAgICAgICAgICAgZm9udC1zaXplOiA4cHg7CiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7CiAgICAgICAgICB9CgogICAgICAgICAgaSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTBweDsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMnB4OwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLnByZXZpZXctY29ubmVjdGlvbnMgewogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgdG9wOiAwOwogICAgICAgICAgbGVmdDogMDsKICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQoKICAvLyDmtYHnqIvlip/og73nrqHnkIbmoLflvI8KICAuZnVuY3Rpb24tbWFuYWdlIHsKICAgIC5mdW5jdGlvbi1hY3Rpb25zIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZ2FwOiA4cHg7CiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgICB9CiAgfQp9CgovLyDlhajlsYDmoLflvI/opobnm5YKOmRlZXAoLml2dS1jYXJkLWJvZHkpIHsKICBwYWRkaW5nOiAxNnB4Owp9Cgo6ZGVlcCguaXZ1LXRhYmxlLXdyYXBwZXIpIHsKICBib3JkZXI6IDFweCBzb2xpZCAjZThlYWVjOwp9CgovLyBCUE1OIFhNTOafpeeci+W8ueeql+agt+W8jwouYnBtbi14bWwtY29udGVudCB7CiAgLnhtbC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgcGFkZGluZy1ib3R0b206IDEycHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZWFlYzsKCiAgICAud29ya2Zsb3ctbmFtZSB7CiAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgY29sb3I6ICMyZDhjZjA7CiAgICB9CiAgfQoKICAueG1sLXZpZXdlciB7CiAgICBtYXgtaGVpZ2h0OiA1MDBweDsKICAgIG92ZXJmbG93OiBhdXRvOwogICAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOGVhZWM7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBwYWRkaW5nOiAxNnB4OwoKICAgIHByZSB7CiAgICAgIG1hcmdpbjogMDsKICAgICAgd2hpdGUtc3BhY2U6IHByZS13cmFwOwogICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7CiAgICAgIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCAnTW9uYWNvJywgJ01lbmxvJywgbW9ub3NwYWNlOwogICAgICBmb250LXNpemU6IDEycHg7CiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7CiAgICAgIGNvbG9yOiAjMzMzOwoKICAgICAgY29kZSB7CiAgICAgICAgYmFja2dyb3VuZDogbm9uZTsKICAgICAgICBwYWRkaW5nOiAwOwogICAgICAgIGJvcmRlcjogbm9uZTsKICAgICAgICBmb250LXNpemU6IGluaGVyaXQ7CiAgICAgICAgY29sb3I6IGluaGVyaXQ7CiAgICAgIH0KICAgIH0KCiAgICAvLyDmu5rliqjmnaHmoLflvI8KICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICAgICAgd2lkdGg6IDhweDsKICAgICAgaGVpZ2h0OiA4cHg7CiAgICB9CgogICAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewogICAgICBiYWNrZ3JvdW5kOiAjZjFmMWYxOwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICB9CgogICAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgewogICAgICBiYWNrZ3JvdW5kOiAjYzFjMWMxOwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjYThhOGE4OwogICAgICB9CiAgICB9CiAgfQoKICAvLyDkuIrmlrDlrZfmrrXnrqHnkIbmoLflvI8KICAuZWxlbWVudC1tYW5hZ2UtY29udGVudCB7CiAgICBtaW4taGVpZ2h0OiA0MDBweDsKICB9CgogIC5lbGVtZW50LWxpc3QgewogICAgcGFkZGluZzogMTZweCAwOwogIH0KCiAgLmFkZC1lbGVtZW50LWJ0biB7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIH0KfQoKCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAi+BA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/workflow", "sourcesContent": ["<template>\n  <div class=\"workflow-list\">\n    <Card>\n      <!-- 搜索区域 -->\n      <Form ref=\"searchForm\" :model=\"searchForm\" inline class=\"search-form\">\n        <FormItem>\n          <Input\n            v-model=\"searchForm.workflowName\"\n            placeholder=\"请输入工作流名称\"\n            style=\"width: 200px\"\n            @on-enter=\"handleSearch\"\n          />\n        </FormItem>\n        <FormItem>\n          <Select\n            v-model=\"searchForm.execute\"\n            placeholder=\"请选择状态\"\n            style=\"width: 120px\"\n            clearable\n          >\n            <Option :value=\"1\">启用</Option>\n            <Option :value=\"0\">停用</Option>\n          </Select>\n        </FormItem>\n        <FormItem>\n          <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\n          <Button @click=\"handleReset\" style=\"margin-left: 8px\">重置</Button>\n        </FormItem>\n      </Form>\n\n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <Button type=\"primary\" icon=\"md-add\" @click=\"handleAdd\">新增流程</Button>\n        <div class=\"right-buttons\">\n          <Button type=\"warning\" icon=\"md-rocket\" @click=\"handleElementManage\" style=\"margin-right: 8px\">上新字段管理</Button>\n          <Button type=\"success\" icon=\"md-settings\" @click=\"handleFunctionManage\">流程功能</Button>\n        </div>\n      </div>\n\n      <!-- 数据表格 -->\n      <Table\n        :columns=\"columns\"\n        :data=\"tableData\"\n        :loading=\"loading\"\n        stripe\n        :max-height=\"600\"\n      >\n        <template v-slot:status=\"{ row }\">\n          <Badge v-if=\"row.execute === 1\" status=\"success\" text=\"启用\" />\n          <Badge v-else status=\"error\" text=\"停用\" />\n        </template>\n\n        <template v-slot:deployStatus=\"{ row }\">\n          <Badge v-if=\"row.deployStatus === 1\" status=\"success\" text=\"已部署\" />\n          <Badge v-else status=\"error\" text=\"未部署\" />\n        </template>\n\n        <template v-slot:nodeCount=\"{ row }\">\n          <Tag color=\"blue\">{{ row.nodeCount || 0 }}个节点</Tag>\n        </template>\n\n        <template v-slot:action=\"{ row }\">\n          <Button v-if=\"row.deployStatus === 0\" type=\"error\" size=\"small\" @click=\"handleDeploy(row)\" style=\"margin-right: 4px;\">部署</Button>\n          <Button type=\"primary\" size=\"small\" @click=\"handleEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\n          <Button\n            :type=\"row.execute === 1 ? 'warning' : 'success'\"\n            size=\"small\"\n            @click=\"handleToggleStatus(row)\"\n            style=\"margin-right: 4px;\"\n          >\n            {{ row.execute === 1 ? '停用' : '启用' }}\n          </Button>\n          <Button type=\"error\" size=\"small\" @click=\"handleDelete(row)\">删除</Button>\n        </template>\n      </Table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <Page\n          :total=\"pageInfo.total\"\n          :current=\"pageInfo.page\"\n          :page-size=\"pageInfo.limit\"\n          show-elevator\n          show-sizer\n          show-total\n          @on-change=\"handlePageChange\"\n          @on-page-size-change=\"handlePageSizeChange\"\n        />\n      </div>\n    </Card>\n\n    <!-- 上新字段管理弹窗 -->\n    <Modal\n      v-model=\"elementManageModal\"\n      title=\"上新字段管理\"\n      width=\"800\"\n      :mask-closable=\"false\"\n      @on-cancel=\"handleElementModalCancel\"\n    >\n      <div class=\"element-manage-content\">\n        <!-- 分类标签 -->\n        <Tabs v-model=\"activeElementType\" @on-click=\"handleElementTypeChange\">\n          <TabPane label=\"基础信息字段\" name=\"1\">\n            <div class=\"element-list\">\n              <!-- 添加按钮 -->\n              <div class=\"add-element-btn\">\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\n                  添加基础信息字段\n                </Button>\n              </div>\n              <!-- 字段列表 -->\n              <Table\n                :columns=\"elementColumns\"\n                :data=\"basicElementList\"\n                :loading=\"false\"\n                size=\"small\"\n                stripe\n              ></Table>\n            </div>\n          </TabPane>\n          <TabPane label=\"标准信息字段\" name=\"2\">\n            <div class=\"element-list\">\n              <!-- 添加按钮 -->\n              <div class=\"add-element-btn\">\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\n                  添加标准信息字段\n                </Button>\n              </div>\n              <!-- 字段列表 -->\n              <Table\n                :columns=\"elementColumns\"\n                :data=\"standardElementList\"\n                :loading=\"false\"\n                size=\"small\"\n                stripe\n              ></Table>\n            </div>\n          </TabPane>\n        </Tabs>\n      </div>\n      <div slot=\"footer\">\n        <Button @click=\"handleElementModalCancel\">关闭</Button>\n      </div>\n    </Modal>\n\n    <!-- 添加/编辑字段弹窗 -->\n    <Modal\n      v-model=\"elementFormModal\"\n      :title=\"elementFormTitle\"\n      width=\"500\"\n      :mask-closable=\"false\"\n      @on-cancel=\"handleElementFormCancel\"\n    >\n      <Form ref=\"elementForm\" :model=\"elementForm\" :rules=\"elementFormRules\" :label-width=\"100\">\n        <FormItem label=\"字段名称\" prop=\"name\">\n          <Input v-model=\"elementForm.name\" placeholder=\"请输入字段名称\" />\n        </FormItem>\n        <FormItem label=\"字段英文\" prop=\"element\">\n          <Input v-model=\"elementForm.element\" placeholder=\"请输入字段英文标识\" />\n        </FormItem>\n        <FormItem label=\"字段类型\" prop=\"type\">\n          <RadioGroup v-model=\"elementForm.type\">\n            <Radio :label=\"1\">基础信息</Radio>\n            <Radio :label=\"2\">标准信息</Radio>\n          </RadioGroup>\n        </FormItem>\n      </Form>\n      <div slot=\"footer\">\n        <Button @click=\"handleElementFormCancel\">取消</Button>\n        <Button type=\"primary\" :loading=\"elementFormLoading\" @click=\"handleElementFormSubmit\">\n          {{ elementForm.id ? '更新' : '添加' }}\n        </Button>\n      </div>\n    </Modal>\n\n    <!-- 流程功能管理弹窗 -->\n    <Modal v-model=\"functionModalVisible\" title=\"流程功能管理\" width=\"80%\" :mask-closable=\"false\">\n      <div class=\"function-manage\">\n        <!-- 功能操作按钮 -->\n        <div class=\"function-actions\">\n          <Button type=\"primary\" icon=\"md-add\" @click=\"handleFunctionAdd\" style=\"margin-right: 4px;\">新增功能</Button>\n          <Button icon=\"md-refresh\" @click=\"loadFunctionData\">刷新</Button>\n        </div>\n\n        <!-- 功能列表表格 -->\n        <Table\n          :columns=\"functionColumns\"\n          :data=\"functionData\"\n          :loading=\"functionLoading\"\n          stripe\n          :max-height=\"400\"\n          style=\"margin-top: 16px;\"\n        >\n          <template v-slot:status=\"{ row }\">\n            <Badge v-if=\"row.status === 0\" status=\"success\" text=\"正常\" />\n            <Badge v-else status=\"error\" text=\"删除\" />\n          </template>\n\n          <template v-slot:action=\"{ row }\">\n            <Button type=\"primary\" size=\"small\" @click=\"handleFunctionEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\n            <Button type=\"error\" size=\"small\" @click=\"handleFunctionDelete(row)\">删除</Button>\n          </template>\n        </Table>\n      </div>\n\n      <div slot=\"footer\">\n        <Button @click=\"functionModalVisible = false\">关闭</Button>\n      </div>\n    </Modal>\n\n    <!-- 功能编辑弹窗 -->\n    <Modal v-model=\"functionEditModalVisible\" :title=\"functionEditMode === 'add' ? '新增功能' : '编辑功能'\" @on-ok=\"handleFunctionSave\">\n      <Form :model=\"functionForm\" :rules=\"functionRules\" ref=\"functionForm\" :label-width=\"80\">\n        <FormItem label=\"功能KEY\" prop=\"key\">\n          <Input v-model=\"functionForm.key\" placeholder=\"请输入功能KEY\" :disabled=\"functionEditMode === 'edit'\" />\n        </FormItem>\n        <FormItem label=\"功能名称\" prop=\"name\">\n          <Input v-model=\"functionForm.name\" placeholder=\"请输入功能名称\" />\n        </FormItem>\n      </Form>\n    </Modal>\n\n    <!-- BPMN XML查看弹窗 -->\n    <Modal v-model=\"bpmnViewModalVisible\" title=\"BPMN XML内容\" width=\"80%\" :mask-closable=\"false\">\n      <div class=\"bpmn-xml-content\">\n        <div class=\"xml-header\">\n          <span class=\"workflow-name\">{{ currentBpmnWorkflow.workflowName }}</span>\n          <Button type=\"primary\" size=\"small\" @click=\"copyBpmnXml\" style=\"float: right;\">\n            <Icon type=\"md-copy\" />\n            复制XML\n          </Button>\n        </div>\n        <div class=\"xml-viewer\">\n          <pre><code>{{ currentBpmnXml }}</code></pre>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <Button @click=\"bpmnViewModalVisible = false\">关闭</Button>\n      </div>\n    </Modal>\n  </div>\n</template>\n\n<script>\nimport workflowApi from '@/api/base/workflow'\nimport funElementApi from '@/api/base/funElement'\n\nexport default {\n  name: 'WorkflowList',\n  data() {\n    return {\n      // 搜索表单\n      searchForm: {\n        workflowName: '',\n        execute: null\n      },\n\n      // 表格列定义\n      columns: [\n        {\n          title: '序号',\n          type: 'index',\n          width: 50,\n          align: 'center'\n        },\n        {\n          title: '工作流Key',\n          key: 'workflowKey',\n          width: 150,\n          align: 'center'\n        },\n        {\n          title: '工作流名称',\n          key: 'workflowName',\n          minWidth: 150\n        },\n        {\n          title: 'BPMN文件',\n          key: 'bpmnXml',\n          width: 120,\n          align: 'center',\n          render: (h, params) => {\n            if (params.row.bpmnXml) {\n              return h('Button', {\n                props: {\n                  type: 'text',\n                  size: 'small'\n                },\n                on: {\n                  click: () => {\n                    this.handleViewBpmn(params.row)\n                  }\n                }\n              }, '查看文件')\n            } else {\n              return h('span', '无文件')\n            }\n          }\n        },\n        {\n          title: '状态',\n          key: 'execute',\n          width: 80,\n          align: 'center',\n          slot: 'status'\n        },\n        {\n          title: '部署状态',\n          key: 'deployStatus',\n          width: 150,\n          align: 'center',\n          slot: 'deployStatus'\n        },\n        {\n          title: '创建人',\n          key: 'createUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '更新人',\n          key: 'updateUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '创建时间',\n          key: 'createTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '更新时间',\n          key: 'updateTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 280,\n          align: 'center',\n          slot: 'action'\n        }\n      ],\n\n      // 表格数据\n      tableData: [],\n\n      // 加载状态\n      loading: false,\n\n      // 分页信息\n      pageInfo: {\n        page: 1,\n        limit: 10,\n        total: 0\n      },\n      currentWorkflow: {},\n\n      // BPMN查看相关\n      bpmnViewModalVisible: false,\n      currentBpmnWorkflow: {},\n      currentBpmnXml: '',\n\n      // 上新字段管理相关数据\n      elementManageModal: false,\n      activeElementType: '1', // 1: 基础信息, 2: 标准信息\n      basicElementList: [],\n      standardElementList: [],\n      elementFormModal: false,\n      elementFormLoading: false,\n      elementForm: {\n        id: null,\n        name: '',\n        element: '',\n        type: 1,\n        funType: 10001 // 上新功能类型枚举\n      },\n      // 表格列定义\n      elementColumns: [\n        {\n          title: '序号',\n          type: 'index',\n          width: 60,\n          align: 'center'\n        },\n        {\n          title: '字段名称',\n          key: 'name',\n          minWidth: 120\n        },\n        {\n          title: '字段英文',\n          key: 'element',\n          minWidth: 150,\n          render: (h, params) => {\n            return h('code', {\n              style: {\n                background: '#f5f5f5',\n                padding: '2px 6px',\n                borderRadius: '3px',\n                fontSize: '12px',\n                fontFamily: 'Courier New, monospace'\n              }\n            }, params.row.element)\n          }\n        },\n        {\n          title: '创建时间',\n          key: 'createTime',\n          width: 150,\n          render: (h, params) => {\n            return h('span', params.row.createTime ? params.row.createTime : '-')\n          }\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 200,\n          align: 'center',\n          render: (h, params) => {\n            return h('div', [\n              h('Button', {\n                props: {\n                  type: 'primary',\n                  size: 'small'\n                },\n                style: {\n                  marginRight: '5px'\n                },\n                on: {\n                  click: () => {\n                    this.handleEditElement(params.row)\n                  }\n                }\n              }, [\n                '编辑'\n              ]),\n              h('Button', {\n                props: {\n                  type: 'error',\n                  size: 'small'\n                },\n                on: {\n                  click: () => {\n                    this.handleDeleteElement(params.row)\n                  }\n                }\n              }, [\n                '删除'\n              ])\n            ])\n          }\n        }\n      ],\n      elementFormRules: {\n        name: [\n          { required: true, message: '请输入字段名称', trigger: 'blur' }\n        ],\n        element: [\n          { required: true, message: '请输入字段英文标识', trigger: 'blur' },\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段英文必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, type: 'number', message: '请选择字段类型', trigger: 'change' }\n        ]\n      },\n\n      // 流程功能管理相关\n      functionModalVisible: false,\n      functionEditModalVisible: false,\n      functionEditMode: 'add', // add | edit\n      functionLoading: false,\n      functionData: [],\n      functionForm: {\n        id: null,\n        key: '',\n        name: ''\n      },\n      functionRules: {\n        key: [\n          { required: true, message: '请输入功能KEY', trigger: 'blur' },\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '功能KEY必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        name: [\n          { required: true, message: '请输入功能名称', trigger: 'blur' }\n        ]\n      },\n      functionColumns: [\n        {\n          title: '序号',\n          type: 'index',\n          width: 60,\n          align: 'center'\n        },\n        {\n          title: '功能KEY',\n          key: 'key',\n          minWidth: 150\n        },\n        {\n          title: '功能名称',\n          key: 'name',\n          minWidth: 150\n        },\n        {\n          title: '创建人',\n          key: 'createUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '更新人',\n          key: 'updateUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '创建时间',\n          key: 'createTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '更新时间',\n          key: 'updateTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 150,\n          align: 'center',\n          slot: 'action'\n        }\n      ]\n    }\n  },\n\n  computed: {\n    // 表单标题\n    elementFormTitle() {\n      return this.elementForm.id ? '编辑字段' : '添加字段'\n    }\n  },\n\n  mounted() {\n    this.loadTableData()\n  },\n\n  methods: {\n    // 加载表格数据\n    async loadTableData() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pageInfo.page,\n          size: this.pageInfo.limit,\n          workflowName: this.searchForm.workflowName,\n          execute: this.searchForm.execute\n        }\n\n        const response = await workflowApi.getWorkflowPage(params)\n        if (response.code === 0) {\n          this.tableData = response.data.records || []\n          this.pageInfo.total = Number(response.data.total) || 0\n        } else {\n          this.$Message.error(response.message || '获取工作流列表失败')\n        }\n      } catch (error) {\n        console.error('获取工作流列表失败:', error)\n        this.$Message.error('获取工作流列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 搜索\n    handleSearch() {\n      console.log('搜索条件:', this.searchForm)\n      this.pageInfo.page = 1\n      this.loadTableData()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        workflowName: '',\n        execute: null\n      }\n      this.handleSearch()\n    },\n\n    // 刷新\n    handleRefresh() {\n      this.loadTableData()\n    },\n\n    // 新增流程\n    handleAdd() {\n      this.$router.push('/base/workflow/add')\n    },\n\n    // 查看BPMN文件\n    handleViewBpmn(row) {\n      if (row.bpmnXml) {\n        this.currentBpmnWorkflow = row\n        this.currentBpmnXml = row.bpmnXml\n        this.bpmnViewModalVisible = true\n      } else {\n        this.$Message.warning('该工作流暂无BPMN文件')\n      }\n    },\n\n    // 复制BPMN XML内容\n    copyBpmnXml() {\n      if (navigator.clipboard && navigator.clipboard.writeText) {\n        navigator.clipboard.writeText(this.currentBpmnXml).then(() => {\n          this.$Message.success('BPMN XML已复制到剪贴板')\n        }).catch(() => {\n          this.$Message.error('复制失败，请手动复制')\n        })\n      } else {\n        // 兼容旧浏览器\n        const textArea = document.createElement('textarea')\n        textArea.value = this.currentBpmnXml\n        document.body.appendChild(textArea)\n        textArea.select()\n        try {\n          document.execCommand('copy')\n          this.$Message.success('BPMN XML已复制到剪贴板')\n        } catch (err) {\n          this.$Message.error('复制失败，请手动复制')\n        }\n        document.body.removeChild(textArea)\n      }\n    },\n\n    // 编辑工作流定义\n    handleEdit(row) {\n      this.$router.push(`/base/workflow/add/${row.id}`)\n    },\n\n    // 删除工作流定义\n    handleDelete(row) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        content: `确定要删除\"${row.workflowName}\"工作流吗？删除后不可恢复。`,\n        onOk: async () => {\n          try {\n            const response = await workflowApi.deleteWorkflow(row.id)\n            if (response.code === 0) {\n              this.$Message.success(response.message || '工作流删除成功')\n              // 重新加载数据\n              this.loadTableData()\n            } else {\n              this.$Message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除工作流失败:', error)\n            this.$Message.error('删除失败，请重试')\n          }\n        }\n      })\n    },\n\n    // 切换启用/停用状态\n    async handleToggleStatus(row) {\n      try {\n        const newStatus = row.execute === 1 ? 0 : 1\n        const statusText = newStatus === 1 ? '启用' : '停用'\n\n        const updateData = {\n          id: row.id,\n          execute: newStatus\n        }\n\n        const response = await workflowApi.updateWorkflow(updateData)\n        if (response.code === 0) {\n          this.$Message.success(`${statusText}成功`)\n          // 更新本地数据\n          row.execute = newStatus\n        } else {\n          this.$Message.error(response.message || `${statusText}失败`)\n        }\n      } catch (error) {\n        console.error('切换状态失败:', error)\n        this.$Message.error('操作失败，请重试')\n      }\n    },\n\n    // ========== 上新字段管理相关方法 ==========\n\n    // 上新字段管理\n    handleElementManage() {\n      this.elementManageModal = true\n      this.activeElementType = '1'\n      this.loadElementData()\n    },\n\n    // 加载字段数据\n    async loadElementData() {\n      try {\n        const response = await funElementApi.getList({\n          funType: 10001 // 上新功能类型\n        })\n\n        if (response.code === 0) {\n          const allElements = response.data || []\n          // 按类型分组\n          this.basicElementList = allElements.filter(item => item.type === 1)\n          this.standardElementList = allElements.filter(item => item.type === 2)\n        } else {\n          this.$Message.error(response.message || '加载字段数据失败')\n        }\n      } catch (error) {\n        console.error('加载字段数据失败:', error)\n        this.$Message.error('加载字段数据失败')\n      }\n    },\n\n    // 切换字段类型\n    handleElementTypeChange(name) {\n      this.activeElementType = name\n    },\n\n    // 添加字段\n    handleAddElement() {\n      this.elementForm = {\n        id: null,\n        name: '',\n        element: '',\n        type: parseInt(this.activeElementType),\n        funType: 10001\n      }\n      this.elementFormModal = true\n\n      // 清除表单验证状态\n      this.$nextTick(() => {\n        if (this.$refs.elementForm) {\n          this.$refs.elementForm.clearValidate()\n        }\n      })\n    },\n\n    // 编辑字段\n    handleEditElement(item) {\n      this.elementForm = {\n        id: item.id,\n        name: item.name,\n        element: item.element,\n        type: item.type,\n        funType: item.funType\n      }\n      this.elementFormModal = true\n\n      // 清除表单验证状态\n      this.$nextTick(() => {\n        if (this.$refs.elementForm) {\n          this.$refs.elementForm.clearValidate()\n        }\n      })\n    },\n\n    // 删除字段\n    handleDeleteElement(item) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        content: `确定要删除字段\"${item.name}\"吗？`,\n        onOk: async () => {\n          try {\n            const response = await funElementApi.delete(item.id)\n            if (response.code === 0) {\n              this.$Message.success('删除成功')\n              this.loadElementData()\n            } else {\n              this.$Message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除字段失败:', error)\n            this.$Message.error('删除失败')\n          }\n        }\n      })\n    },\n\n    // 关闭字段管理弹窗\n    handleElementModalCancel() {\n      this.elementManageModal = false\n    },\n\n    // 提交字段表单\n    async handleElementFormSubmit() {\n      console.log('提交表单，当前表单数据:', this.elementForm)\n      this.$refs.elementForm.validate(async (valid) => {\n        console.log('表单验证结果:', valid)\n        if (valid) {\n          this.elementFormLoading = true\n          try {\n            const isEdit = !!this.elementForm.id\n            let response\n\n            if (isEdit) {\n              response = await funElementApi.update(this.elementForm.id, this.elementForm)\n            } else {\n              response = await funElementApi.create(this.elementForm)\n            }\n\n            if (response.code === 0) {\n              this.$Message.success(isEdit ? '更新成功' : '添加成功')\n              this.elementFormModal = false\n              this.loadElementData()\n            } else {\n              this.$Message.error(response.message || '操作失败')\n            }\n          } catch (error) {\n            console.error('保存字段失败:', error)\n            this.$Message.error('操作失败')\n          } finally {\n            this.elementFormLoading = false\n          }\n        }\n      })\n    },\n\n    // 取消字段表单\n    handleElementFormCancel() {\n      this.elementFormModal = false\n      // 重置表单数据和验证状态\n      this.$nextTick(() => {\n        if (this.$refs.elementForm) {\n          this.$refs.elementForm.resetFields()\n        }\n      })\n    },\n\n    // 工作流部署按钮\n    handleDeploy(row) {\n      if (!row.bpmnXml) {\n        this.$Message.warning('该工作流没有BPMN文件，无法部署')\n        return\n      }\n      this.$Modal.confirm({\n        title: '部署确认',\n        content: `确定要部署 \"${row.workflowName}\" 到工作流引擎吗？`,\n        onOk: async () => {\n          try {\n            this.$Message.loading('正在部署工作流...')\n            const response = await workflowApi.erpDeployWorkflow(row)\n\n            if (response.code === 0) {\n              this.$Message.success('工作流部署成功！')\n              console.log('部署结果:', response.data)\n\n              // 可以在这里更新表格数据，标记为已部署\n              this.loadTableData()\n            } else {\n              this.$Message.error(response.message || '工作流部署失败')\n            }\n          } catch (error) {\n            console.error('工作流部署失败:', error)\n            this.$Message.error('工作流部署失败: ' + (error.message || '未知错误'))\n          }\n        }\n      })\n    },\n\n    // 分页变更\n    handlePageChange(page) {\n      this.pageInfo.page = Number(page)\n      this.loadTableData()\n    },\n\n    // 页面大小变更\n    handlePageSizeChange(pageSize) {\n      this.pageInfo.limit = Number(pageSize)\n      this.pageInfo.page = 1\n      this.loadTableData()\n    },\n\n    // ========== 流程功能管理相关方法 ==========\n\n    // 打开流程功能管理弹窗\n    handleFunctionManage() {\n      this.functionModalVisible = true\n      this.loadFunctionData()\n    },\n\n    // 加载功能数据\n    async loadFunctionData() {\n      this.functionLoading = true\n\n      try {\n        const response = await workflowApi.getFunctionTypeList()\n        if (response.code === 0) {\n          this.functionData = response.data.records || []\n        } else {\n          this.$Message.error(response.message || '获取功能类型列表失败')\n        }\n      } catch (error) {\n        console.error('获取功能类型列表失败:', error)\n        this.$Message.error('获取功能类型列表失败')\n      } finally {\n        this.functionLoading = false\n      }\n    },\n\n    // 新增功能\n    handleFunctionAdd() {\n      this.functionEditMode = 'add'\n      this.functionForm = {\n        id: null,\n        key: '',\n        name: ''\n      }\n      this.functionEditModalVisible = true\n      this.$nextTick(() => {\n        this.$refs.functionForm.resetFields()\n      })\n    },\n\n    // 编辑功能\n    handleFunctionEdit(row) {\n      this.functionEditMode = 'edit'\n      this.functionForm = {\n        id: row.id,\n        key: row.key,\n        name: row.name\n      }\n      this.functionEditModalVisible = true\n    },\n\n    // 保存功能\n    handleFunctionSave() {\n      this.$refs.functionForm.validate(async (valid) => {\n        if (valid) {\n          try {\n            let response\n            if (this.functionEditMode === 'add') {\n              // 新增功能\n              response = await workflowApi.addFunctionType(this.functionForm)\n            } else {\n              // 编辑功能\n              response = await workflowApi.updateFunctionType(this.functionForm)\n            }\n\n            if (response.code === 0) {\n              this.$Message.success(response.message || (this.functionEditMode === 'add' ? '功能新增成功' : '功能编辑成功'))\n              this.functionEditModalVisible = false\n              // 重新加载数据\n              this.loadFunctionData()\n            } else {\n              this.$Message.error(response.message || '操作失败')\n            }\n          } catch (error) {\n            console.error('保存功能失败:', error)\n            this.$Message.error('操作失败，请重试')\n          }\n        }\n      })\n    },\n\n    // 删除功能\n    handleFunctionDelete(row) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        content: `确定要删除功能\"${row.name}\"吗？此操作不可恢复。`,\n        onOk: async () => {\n          try {\n            const response = await workflowApi.deleteFunctionType(row.id)\n            if (response.code === 0) {\n              this.$Message.success(response.message || '功能删除成功')\n              // 重新加载数据\n              this.loadFunctionData()\n            } else {\n              this.$Message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除功能失败:', error)\n            this.$Message.error('删除失败，请重试')\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n\n\n<style lang=\"less\" scoped>\n.workflow-list {\n  padding: 16px;\n\n  .search-form {\n    margin-bottom: 16px;\n\n    .ivu-form-item {\n      margin-bottom: 0;\n      margin-right: 16px;\n    }\n  }\n\n  .action-buttons {\n    margin-bottom: 16px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 8px;\n\n    .right-buttons {\n      display: flex;\n      gap: 8px;\n    }\n  }\n\n  .pagination-wrapper {\n    margin-top: 16px;\n    text-align: right;\n  }\n\n  .workflow-view {\n    .workflow-info {\n      margin-bottom: 16px;\n\n      .info-item {\n        margin-bottom: 12px;\n        display: flex;\n        align-items: center;\n\n        .info-label {\n          font-weight: 500;\n          color: #515a6e;\n          min-width: 80px;\n        }\n\n        .info-value {\n          color: #17233d;\n        }\n      }\n    }\n\n    .workflow-preview {\n      .preview-canvas {\n        position: relative;\n        width: 100%;\n        height: 400px;\n        background: #f8f9fa;\n        border: 1px solid #e8eaec;\n        border-radius: 4px;\n        overflow: hidden;\n\n        .preview-node {\n          position: absolute;\n          width: 50px;\n          height: 25px;\n          background: white;\n          border: 1px solid #dcdee2;\n          border-radius: 3px;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          font-size: 10px;\n          user-select: none;\n\n          &.node-start {\n            background: #52c41a;\n            color: white;\n            border-color: #52c41a;\n          }\n\n          &.node-end {\n            background: #f5222d;\n            color: white;\n            border-color: #f5222d;\n          }\n\n          &.node-approval {\n            background: #1890ff;\n            color: white;\n            border-color: #1890ff;\n          }\n\n          &.node-condition {\n            background: #fa8c16;\n            color: white;\n            border-color: #fa8c16;\n            transform: rotate(45deg);\n\n            .node-title {\n              transform: rotate(-45deg);\n            }\n          }\n\n          &.node-task {\n            background: #f0f0f0;\n            color: #333;\n          }\n\n          .node-title {\n            font-size: 8px;\n            font-weight: 500;\n            text-align: center;\n            line-height: 1;\n          }\n\n          i {\n            font-size: 10px;\n            margin-bottom: 2px;\n          }\n        }\n\n        .preview-connections {\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          pointer-events: none;\n        }\n      }\n    }\n  }\n\n  // 流程功能管理样式\n  .function-manage {\n    .function-actions {\n      display: flex;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n  }\n}\n\n// 全局样式覆盖\n:deep(.ivu-card-body) {\n  padding: 16px;\n}\n\n:deep(.ivu-table-wrapper) {\n  border: 1px solid #e8eaec;\n}\n\n// BPMN XML查看弹窗样式\n.bpmn-xml-content {\n  .xml-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #e8eaec;\n\n    .workflow-name {\n      font-size: 16px;\n      font-weight: 600;\n      color: #2d8cf0;\n    }\n  }\n\n  .xml-viewer {\n    max-height: 500px;\n    overflow: auto;\n    background: #f8f9fa;\n    border: 1px solid #e8eaec;\n    border-radius: 4px;\n    padding: 16px;\n\n    pre {\n      margin: 0;\n      white-space: pre-wrap;\n      word-wrap: break-word;\n      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;\n      font-size: 12px;\n      line-height: 1.5;\n      color: #333;\n\n      code {\n        background: none;\n        padding: 0;\n        border: none;\n        font-size: inherit;\n        color: inherit;\n      }\n    }\n\n    // 滚动条样式\n    &::-webkit-scrollbar {\n      width: 8px;\n      height: 8px;\n    }\n\n    &::-webkit-scrollbar-track {\n      background: #f1f1f1;\n      border-radius: 4px;\n    }\n\n    &::-webkit-scrollbar-thumb {\n      background: #c1c1c1;\n      border-radius: 4px;\n\n      &:hover {\n        background: #a8a8a8;\n      }\n    }\n  }\n\n  // 上新字段管理样式\n  .element-manage-content {\n    min-height: 400px;\n  }\n\n  .element-list {\n    padding: 16px 0;\n  }\n\n  .add-element-btn {\n    margin-bottom: 16px;\n  }\n}\n\n\n</style>\n"]}]}