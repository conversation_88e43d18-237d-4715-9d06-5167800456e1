package com.aimo.base.server.message;

import com.aimo.base.client.model.basf.BaseProduct;
import com.aimo.base.client.model.prediction.PredictionStockLock;
import com.aimo.base.client.model.prediction.PredictionVersionAsin;
import com.aimo.base.server.feign.ys.YsOrgBaseClient;
import com.aimo.base.server.feign.ys.YsOthOutOrderClient;
import com.aimo.base.server.feign.ys.YsProductBaseClient;
import com.aimo.base.server.service.basf.BaseProductService;
import com.aimo.base.server.service.prediction.PredictionStockLockService;
import com.aimo.base.server.service.prediction.SalePredictionAsinService;
import com.aimo.common.exception.OpenAlertException;
import com.aimo.common.model.ResultBody;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.DateUtils;
import com.aimo.common.utils.StringUtils;
import com.sy.ys.client.model.base.YsOrg;
import com.sy.ys.client.model.base.YsProduct;
import com.sy.ys.client.param.othOrder.YsOthOutOrderDetailParam;
import com.sy.ys.client.param.othOrder.YsOthOutOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class StockLockSyncYsOthOutOrderAction implements MessageAction {
    @Resource
    private PredictionStockLockService stockLockService;
    @Resource
    private YsOthOutOrderClient ysOthOutOrderClient;
    @Resource
    private YsOrgBaseClient ysOrgBaseClient;
    @Resource
    private YsProductBaseClient ysProductBaseClient;
    @Resource
    private BaseProductService baseProductService;
    @Resource
    private SalePredictionAsinService asinService;

    @Override
    public boolean execute(Long userId, String message) {
        try {
            Map<String, Object> param = this.getMessage(message);
            Long id = StringUtils.toLong(param.get("id"), null);
            if (id == null) {
                return true;
            }
            PredictionStockLock stockLock = stockLockService.getById(id);
            if(stockLock.getYsId() != null){
                ResultBody<Boolean> delResultBody = ysOthOutOrderClient.delOthOutOrder(stockLock.getYsId());
                if(!delResultBody.isOk() || !StringUtils.toBoolean(delResultBody.getData())){
                    return true;
                }
                stockLock.setYsId(null);
                stockLock.setYsMessage(null);
            }
            if(stockLock.getLockQty()>0){
                YsOrg ysOrg = ysOrgBaseClient.getByCode("A01");
                Map<Long, String> unitCodeMap = new HashMap<>();
                Map<Long, Integer> idQtyMap = getIdQty(stockLock.getProductId(), stockLock.getParentId(), stockLock.getLockQty(),unitCodeMap);
                YsOthOutOrderParam ysOthOutOrderParam = new YsOthOutOrderParam();
                ysOthOutOrderParam.setBustype("A10016");
                ysOthOutOrderParam.setBillDate(DateUtils.formatDate(DateUtils.getCurrentDate(), DateUtils.DATE_TYPE_YMD));
                ysOthOutOrderParam.setOrgId(ysOrg.getId());
                ysOthOutOrderParam.setDetailList(idQtyMap.keySet().stream().map(productId -> {
                    int thisQty = idQtyMap.get(productId);
                    YsOthOutOrderDetailParam detailParam = new YsOthOutOrderDetailParam();
                    detailParam.setQty(thisQty);
                    detailParam.setStockUnitId(unitCodeMap.get(productId));
                    detailParam.setContactsQuantity(thisQty);
                    detailParam.setProductId(productId);
                    detailParam.setSubQty(thisQty);
                    return detailParam;
                }).collect(Collectors.toList()));
                ResultBody<Long> resultBody = ysOthOutOrderClient.addOthOutOrder(ysOthOutOrderParam);
                if (resultBody.isOk() && resultBody.getData() != null) {
                    stockLock.setYsId(StringUtils.toLong(resultBody.getData()));
                    stockLock.setYsMessage(null);
                }else{
                    stockLock.setYsId(null);
                    stockLock.setYsMessage(resultBody.getMessage());
                }
            }
            stockLockService.updateById(stockLock);
            return true;
        } catch (Exception e) {
            if (e instanceof OpenAlertException) {
                throw (OpenAlertException) e;
            } else {
                throw new OpenAlertException(e.getMessage());
            }
        }
    }

    private Map<Long, Integer> getIdQty(Long productId, Long parentId,int lockQty, Map<Long, String> unitCodeMap) {
        if (parentId != null) {
            PredictionVersionAsin predictionAsin = asinService.getById(parentId);
            productId = predictionAsin.getProductId();
        }
        BaseProduct baseProduct = baseProductService.getBy(productId, null);
        Map<String, Integer> skuMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(baseProduct.getDetailList())) {
            baseProduct.getDetailList().forEach(item -> skuMap.put(item.getChildSku(), item.getQuantity() * lockQty));
        } else {
            skuMap.put(baseProduct.getCode(), lockQty);
        }

        List<YsProduct> ysProductList = ysProductBaseClient.listByCode(new ArrayList<>(skuMap.keySet()));
        Map<String, Long> idMap = ysProductList.stream().peek(item -> unitCodeMap.put(item.getId(), item.getUnitCode())).collect(Collectors.toMap(YsProduct::getCode, YsProduct::getId, (o1, o2) -> o1));
        Map<Long, Integer> idQtyMap = new HashMap<>();
        skuMap.forEach((code, qty) -> {
            Long id = idMap.get(code);
            if (id == null) {
                throw new OpenAlertException("YS物料不存在" + code);
            }
            idQtyMap.put(idMap.get(code), qty);
        });
        return idQtyMap;
    }
}
