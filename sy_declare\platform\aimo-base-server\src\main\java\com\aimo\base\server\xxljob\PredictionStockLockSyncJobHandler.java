package com.aimo.base.server.xxljob;

import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.model.prediction.PredictionStockLock;
import com.aimo.base.server.service.prediction.impl.PredictionStockLockServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/*
 *  刷新可分数库存和YS的库存数据为一致
 */
@Component
public class PredictionStockLockSyncJobHandler extends IJobHandler {
    @Resource
    private PredictionStockLockServiceImpl lockService;

    @Override
    @XxlJob(value = "PredictionStockLockSyncJobHandler")
    public ReturnT<String> execute(String s) {
        QueryWrapper<PredictionStockLock> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PredictionStockLock::getStatus, BaseConstants.STATUS0).gt(PredictionStockLock::getLockQty, 0);
        lockService.list(queryWrapper).forEach(item -> lockService.sendMessageNoTransaction(1L, "admin", "StockLockSyncYsOthOutOrderAction", "{\"id\":\"" + item.getId() + "\"}","StockLockSyncYsOthOutOrderAction"));
        return ReturnT.SUCCESS;
    }
}
