-- 功能字段元素表
CREATE TABLE `base_fun_element` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名称',
  `element` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段英文',
  `fun_type` int NOT NULL COMMENT '功能枚举值：上新功能10001',
  `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '类型 1：基础信息 2：标准信息',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  `status` tinyint(1) unsigned zerofill DEFAULT '0' COMMENT '0：正常 1：删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_element_fun_type` (`element`, `fun_type`, `status`) COMMENT '字段英文在同一功能类型下唯一',
  KEY `idx_fun_type` (`fun_type`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='功能字段元素表';

-- 插入一些示例数据
INSERT INTO `base_fun_element` (`name`, `element`, `fun_type`, `type`, `create_user`, `create_time`, `update_user`, `update_time`, `status`) VALUES
('产品名称', 'product_name', 10001, 1, 1, NOW(), 1, NOW(), 0),
('产品SKU', 'product_sku', 10001, 1, 1, NOW(), 1, NOW(), 0),
('产品分类', 'product_category', 10001, 1, 1, NOW(), 1, NOW(), 0),
('产品价格', 'product_price', 10001, 1, 1, NOW(), 1, NOW(), 0),
('产品重量', 'product_weight', 10001, 2, 1, NOW(), 1, NOW(), 0),
('产品尺寸', 'product_dimensions', 10001, 2, 1, NOW(), 1, NOW(), 0),
('产品材质', 'product_material', 10001, 2, 1, NOW(), 1, NOW(), 0),
('产品颜色', 'product_color', 10001, 2, 1, NOW(), 1, NOW(), 0);
