package com.aimo.base.server.service.base;

import com.aimo.base.client.model.base.WorkflowFunctionType;
import com.aimo.common.mybatis.base.service.IBaseService;
import com.aimo.common.model.PageParams;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 工作流功能类型服务接口
 *
 * <AUTHOR>
 */
public interface WorkflowFunctionTypeService extends IBaseService<WorkflowFunctionType> {
    
    /**
     * 分页查询工作流功能类型
     *
     * @param pageParams 分页参数
     * @return 分页结果
     */
    IPage<WorkflowFunctionType> listPage(PageParams<WorkflowFunctionType> pageParams);
    
    /**
     * 根据Key查询工作流功能类型
     *
     * @param key 功能Key
     * @return 工作流功能类型
     */
    WorkflowFunctionType getByKey(String key);
    
    /**
     * 检查Key是否存在
     *
     * @param key 功能Key
     * @return 是否存在
     */
    boolean checkKey(String key);
    
    /**
     * 保存工作流功能类型
     *
     * @param workflowFunctionType 工作流功能类型
     * @return 是否成功
     */
    boolean saveWorkflowFunctionType(WorkflowFunctionType workflowFunctionType);
    
    /**
     * 更新工作流功能类型
     *
     * @param workflowFunctionType 工作流功能类型
     * @return 是否成功
     */
    boolean updateWorkflowFunctionType(WorkflowFunctionType workflowFunctionType);
    
    /**
     * 删除工作流功能类型
     *
     * @param id 工作流功能类型ID
     * @return 是否成功
     */
    boolean deleteWorkflowFunctionType(Long id);
    
    /**
     * 获取所有正常状态的工作流功能类型
     *
     * @return 工作流功能类型列表
     */
    List<WorkflowFunctionType> getAllNormalTypes();
}
