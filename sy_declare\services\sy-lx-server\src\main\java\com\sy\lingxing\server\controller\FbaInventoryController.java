package com.sy.lingxing.server.controller;


import com.aimo.common.model.ResultBody;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sy.lingxing.client.model.Replenishment.LxFbaInventory;
import com.sy.lingxing.client.param.FbaInventoryParam;
import com.sy.lingxing.client.service.ILingxingFbaInventoryClient;
import com.sy.lingxing.client.vo.LingShipmentItemVO;
import com.sy.lingxing.server.service.LxFbaInventoryService;
import com.sy.lingxing.server.sync.LxFbaInventorySyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: chiYe
 */
@Api(value = "平台库存", tags = "平台库存")
@RestController
@RequestMapping(value = "/fbaInventory")
public class FbaInventoryController implements ILingxingFbaInventoryClient {
    @Resource
    private LxFbaInventoryService fbaInventoryService;
    @Resource
    private LxFbaInventorySyncService syncService;

    @ApiOperation(value = "平台库存分页查询", notes = "平台库存分页查询")
    @GetMapping(value = "/listPage")
    @ResponseBody
    public ResultBody<IPage<LxFbaInventory>> listPage(FbaInventoryParam param) {
        return ResultBody.ok(fbaInventoryService.listPage(param));
    }

    @ApiOperation(value = "FBA仓库存分页查询", notes = "FBA仓库存分页查询")
    @PostMapping(value = "/listInventory")
    @ResponseBody
    public List<LxFbaInventory> listInventory(@RequestParam(value = "sids", required = false) String sids, @RequestParam(value = "sellerSkus", required = false) String sellerSkus) {
        FbaInventoryParam param = new FbaInventoryParam();
        param.setSids(sids);
        param.setSellerSkus(sellerSkus);
        param.setLimit(-1);
        return fbaInventoryService.listPage(param).getRecords();
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/exportFile")
    public void exportFile(HttpServletResponse response, FbaInventoryParam param) {
        fbaInventoryService.exportFile(response, param);
    }

    @ApiOperation(value = "平台在途明细分页查询", notes = "平台在途明细分页查询")
    @GetMapping(value = "/listInTransitPage")
    @ResponseBody
    public ResultBody<IPage<LingShipmentItemVO>> listInTransitPage(FbaInventoryParam param) {
        return ResultBody.ok(fbaInventoryService.listInTransitPage(param));
    }

    @ApiOperation(value = "FBA仓在途分页查询", notes = "FBA仓在途分页查询")
    @PostMapping(value = "/listInTransit")
    @ResponseBody
    public List<LingShipmentItemVO> listInTransit(@RequestParam(value = "sids", required = false) String sids, @RequestParam(value = "sellerSkus", required = false) String sellerSkus) {
        FbaInventoryParam param = new FbaInventoryParam();
        param.setSids(sids);
        param.setSellerSkus(sellerSkus);
        param.setLimit(-1);
        return fbaInventoryService.listInTransitPage(param).getRecords();
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/exportInTransitFile")
    public void exportInTransitFile(HttpServletResponse response, FbaInventoryParam param) {
        fbaInventoryService.exportInTransitFile(response, param);
    }

    @ApiOperation(value = "同步平台库存", notes = "同步平台库存")
    @PostMapping(value = "/sync")
    @ResponseBody
    public ResultBody<Boolean> sync(@RequestParam(value = "asins") String asins) {
        return ResultBody.ok(!CollectionUtils.isEmpty(syncService.syncFbaInventory(StringUtils.toArrayString(asins, StringUtils.commaSp))));
    }
}
