package com.aimo.base.client.model.base;

import com.aimo.common.mybatis.base.entity.UpdateEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 工作流功能类型实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("base_workflow_function_type")
public class WorkflowFunctionType extends UpdateEntity<WorkflowFunctionType> {
    
    /**
     * 功能KEY
     */
    @TableField(value = "`key`")
    private String key;
    
    /**
     * 功能名称
     */
    private String name;
    
    /**
     * 状态 0:正常 1:删除
     */
    @TableField(value = "`status`")
    private Integer status;
    
    public WorkflowFunctionType(String key, String name, Integer status) {
        super(true);
        this.key = key;
        this.name = name;
        this.status = status;
    }
}
