package com.sy.erp.server.service.impl;

import com.sy.erp.server.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@Service("workflowServiceImpl")
@ConditionalOnBean(name = "workflowSafeProcessEngine")
public class WorkflowServiceImpl implements WorkflowService {

    @Autowired(required = false)
    @Qualifier("workflowSafeProcessEngine")
    private ProcessEngine processEngine;

    @Autowired(required = false)
    @Qualifier("workflowSafeRepositoryService")
    private RepositoryService repositoryService;

    @Autowired(required = false)
    @Qualifier("workflowSafeRuntimeService")
    private RuntimeService runtimeService;

    @Autowired(required = false)
    @Qualifier("workflowSafeTaskService")
    private TaskService taskService;

    @Autowired(required = false)
    @Qualifier("workflowSafeHistoryService")
    private HistoryService historyService;

    @Override
    public Map<String, Object> deployProcess(String processName, String processKey, String bpmnXml) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            Deployment deployment = repositoryService.createDeployment()
                    .name(processName)
                    .key(processKey)
                    .addString(processName + ".bpmn20.xml", bpmnXml)
                    .deploy();

            result.put("success", true);
            result.put("deploymentId", deployment.getId());
            result.put("deploymentName", deployment.getName());
            result.put("message", "流程部署成功");

            log.info("流程部署成功: {}", deployment.getId());

        } catch (Exception e) {
            log.error("流程部署失败", e);
            result.put("success", false);
            result.put("message", "流程部署失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> deleteDeployment(String deploymentId, boolean cascade) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            repositoryService.deleteDeployment(deploymentId, cascade);
            result.put("success", true);
            result.put("message", "部署删除成功");
            log.info("部署删除成功: {}", deploymentId);
        } catch (Exception e) {
            log.error("部署删除失败", e);
            result.put("success", false);
            result.put("message", "部署删除失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> startProcess(String processDefinitionKey, String businessKey, Map<String, Object> variables) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            ProcessInstance processInstance;
            if (businessKey != null) {
                processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
            } else {
                processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, variables);
            }

            result.put("success", true);
            result.put("processInstanceId", processInstance.getId());
            result.put("businessKey", processInstance.getBusinessKey());
            result.put("message", "流程启动成功");
            log.info("流程启动成功: {}", processInstance.getId());
        } catch (Exception e) {
            log.error("流程启动失败", e);
            result.put("success", false);
            result.put("message", "流程启动失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> completeTask(String taskId, Map<String, Object> variables) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            if (variables != null && !variables.isEmpty()) {
                taskService.complete(taskId, variables);
            } else {
                taskService.complete(taskId);
            }

            result.put("success", true);
            result.put("message", "任务完成成功");
            log.info("任务完成成功: {}", taskId);
        } catch (Exception e) {
            log.error("任务完成失败", e);
            result.put("success", false);
            result.put("message", "任务完成失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getProcessDefinitions(Pageable pageable) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery()
                    .orderByProcessDefinitionVersion().desc();

            long total = query.count();
            List<ProcessDefinition> definitions = query
                    .listPage((int) pageable.getOffset(), pageable.getPageSize());

            List<Map<String, Object>> definitionList = definitions.stream().map(def -> {
                Map<String, Object> defMap = new HashMap<>();
                defMap.put("id", def.getId());
                defMap.put("key", def.getKey());
                defMap.put("name", def.getName());
                defMap.put("version", def.getVersion());
                defMap.put("deploymentId", def.getDeploymentId());
                defMap.put("suspended", def.isSuspended());
                return defMap;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", definitionList);
            result.put("total", total);
            result.put("page", pageable.getPageNumber());
            result.put("size", pageable.getPageSize());
        } catch (Exception e) {
            log.error("获取流程定义列表失败", e);
            result.put("success", false);
            result.put("message", "获取流程定义列表失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getProcessInstances(Pageable pageable) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            ProcessInstanceQuery query = runtimeService.createProcessInstanceQuery()
                    .orderByStartTime().desc();

            long total = query.count();
            List<ProcessInstance> instances = query
                    .listPage((int) pageable.getOffset(), pageable.getPageSize());

            List<Map<String, Object>> instanceList = instances.stream().map(instance -> {
                Map<String, Object> instanceMap = new HashMap<>();
                instanceMap.put("id", instance.getId());
                instanceMap.put("businessKey", instance.getBusinessKey());
                instanceMap.put("processDefinitionId", instance.getProcessDefinitionId());
                instanceMap.put("processDefinitionKey", instance.getProcessDefinitionKey());
                instanceMap.put("suspended", instance.isSuspended());
                return instanceMap;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", instanceList);
            result.put("total", total);
            result.put("page", pageable.getPageNumber());
            result.put("size", pageable.getPageSize());
        } catch (Exception e) {
            log.error("获取流程实例列表失败", e);
            result.put("success", false);
            result.put("message", "获取流程实例列表失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getUserTasks(String userId, Pageable pageable) {
        return getAssignedTasks(userId, pageable);
    }

    @Override
    public Map<String, Object> getAssignedTasks(String userId, Pageable pageable) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            TaskQuery query = taskService.createTaskQuery()
                    .taskAssignee(userId)
                    .orderByTaskCreateTime().desc();

            long total = query.count();
            List<Task> tasks = query.listPage((int) pageable.getOffset(), pageable.getPageSize());

            List<Map<String, Object>> taskList = tasks.stream().map(task -> {
                Map<String, Object> taskMap = new HashMap<>();
                taskMap.put("id", task.getId());
                taskMap.put("name", task.getName());
                taskMap.put("assignee", task.getAssignee());
                taskMap.put("createTime", task.getCreateTime());
                taskMap.put("processInstanceId", task.getProcessInstanceId());
                taskMap.put("processDefinitionId", task.getProcessDefinitionId());
                return taskMap;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", taskList);
            result.put("total", total);
            result.put("page", pageable.getPageNumber());
            result.put("size", pageable.getPageSize());
        } catch (Exception e) {
            log.error("获取用户任务列表失败", e);
            result.put("success", false);
            result.put("message", "获取用户任务列表失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getCandidateTasks(String userId, Pageable pageable) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            TaskQuery query = taskService.createTaskQuery()
                    .taskCandidateUser(userId)
                    .orderByTaskCreateTime().desc();

            long total = query.count();
            List<Task> tasks = query.listPage((int) pageable.getOffset(), pageable.getPageSize());

            List<Map<String, Object>> taskList = tasks.stream().map(task -> {
                Map<String, Object> taskMap = new HashMap<>();
                taskMap.put("id", task.getId());
                taskMap.put("name", task.getName());
                taskMap.put("assignee", task.getAssignee());
                taskMap.put("createTime", task.getCreateTime());
                taskMap.put("processInstanceId", task.getProcessInstanceId());
                taskMap.put("processDefinitionId", task.getProcessDefinitionId());
                return taskMap;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", taskList);
            result.put("total", total);
            result.put("page", pageable.getPageNumber());
            result.put("size", pageable.getPageSize());
        } catch (Exception e) {
            log.error("获取候选任务列表失败", e);
            result.put("success", false);
            result.put("message", "获取候选任务列表失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getProcessDiagram(String processInstanceId) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            // 这里可以实现流程图获取逻辑
            result.put("success", true);
            result.put("message", "流程图功能待实现");
        } catch (Exception e) {
            log.error("获取流程图失败", e);
            result.put("success", false);
            result.put("message", "获取流程图失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getProcessHistory(String processInstanceId) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            // 这里可以实现流程历史获取逻辑
            result.put("success", true);
            result.put("message", "流程历史功能待实现");
        } catch (Exception e) {
            log.error("获取流程历史失败", e);
            result.put("success", false);
            result.put("message", "获取流程历史失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> suspendProcessDefinition(String processDefinitionId) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            repositoryService.suspendProcessDefinitionById(processDefinitionId);
            result.put("success", true);
            result.put("message", "流程定义挂起成功");
            log.info("流程定义挂起成功: {}", processDefinitionId);
        } catch (Exception e) {
            log.error("流程定义挂起失败", e);
            result.put("success", false);
            result.put("message", "流程定义挂起失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> activateProcessDefinition(String processDefinitionId) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            repositoryService.activateProcessDefinitionById(processDefinitionId);
            result.put("success", true);
            result.put("message", "流程定义激活成功");
            log.info("流程定义激活成功: {}", processDefinitionId);
        } catch (Exception e) {
            log.error("流程定义激活失败", e);
            result.put("success", false);
            result.put("message", "流程定义激活失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getProcessVariables(String processInstanceId) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            result.put("success", true);
            result.put("data", variables);
            result.put("message", "获取流程变量成功");
        } catch (Exception e) {
            log.error("获取流程变量失败", e);
            result.put("success", false);
            result.put("message", "获取流程变量失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> setProcessVariables(String processInstanceId, Map<String, Object> variables) {
        Map<String, Object> result = new HashMap<>();

        if (!isWorkflowEngineAvailable()) {
            return createUnavailableResponse();
        }

        try {
            runtimeService.setVariables(processInstanceId, variables);
            result.put("success", true);
            result.put("message", "设置流程变量成功");
            log.info("设置流程变量成功: {}", processInstanceId);
        } catch (Exception e) {
            log.error("设置流程变量失败", e);
            result.put("success", false);
            result.put("message", "设置流程变量失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 检查工作流引擎是否可用
     */
    private boolean isWorkflowEngineAvailable() {
        return processEngine != null && repositoryService != null && runtimeService != null && taskService != null;
    }

    /**
     * 创建不可用响应
     */
    private Map<String, Object> createUnavailableResponse() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "工作流引擎未初始化，可能是配置未加载或数据库连接失败");
        result.put("code", "WORKFLOW_ENGINE_NOT_INITIALIZED");
        result.put("timestamp", System.currentTimeMillis());

        Map<String, Object> help = new HashMap<>();
        help.put("checkNacos", "检查Nacos配置中心的workflow_properties配置");
        help.put("checkDatabase", "确保数据库连接正常");
        help.put("checkLogs", "查看应用启动日志中的工作流初始化信息");
        result.put("help", help);

        return result;
    }
}
