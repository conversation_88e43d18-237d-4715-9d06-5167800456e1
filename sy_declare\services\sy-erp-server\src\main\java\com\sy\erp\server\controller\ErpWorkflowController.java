package com.sy.erp.server.controller;

import com.aimo.base.client.model.base.Workflow;
import com.sy.erp.server.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * ERP工作流控制器 - 处理 /erp/workflow 路径的请求
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@RestController
@RequestMapping("/erp/workflow")
@Api(tags = "ERP工作流管理")
public class ErpWorkflowController {

    @Autowired
    private WorkflowService workflowService;

    @PostMapping("/deploy")
    @ApiOperation("部署流程定义")
    public Map<String, Object> deployProcess(@RequestBody Workflow workflow) {
        log.info("收到ERP工作流部署请求: {}", workflow != null ? workflow.getWorkflowName() : "null");
        return workflowService.deployProcess(
            workflow.getWorkflowName(), 
            workflow.getWorkflowKey(), 
            workflow.getBpmnXml()
        );
    }

    @DeleteMapping("/deployment/{deploymentId}")
    @ApiOperation("删除部署")
    public Map<String, Object> deleteDeployment(
            @ApiParam("部署ID") @PathVariable String deploymentId,
            @ApiParam("是否级联删除") @RequestParam(defaultValue = "false") boolean cascade) {
        return workflowService.deleteDeployment(deploymentId, cascade);
    }

    @PostMapping("/start")
    @ApiOperation("启动流程实例")
    public Map<String, Object> startProcess(
            @ApiParam("流程定义Key") @RequestParam String processDefinitionKey,
            @ApiParam("业务Key") @RequestParam(required = false) String businessKey,
            @ApiParam("流程变量") @RequestBody(required = false) Map<String, Object> variables) {
        return workflowService.startProcess(processDefinitionKey, businessKey, variables);
    }

    @PostMapping("/task/{taskId}/complete")
    @ApiOperation("完成任务")
    public Map<String, Object> completeTask(
            @ApiParam("任务ID") @PathVariable String taskId,
            @ApiParam("任务变量") @RequestBody(required = false) Map<String, Object> variables) {
        return workflowService.completeTask(taskId, variables);
    }

    @GetMapping("/definitions")
    @ApiOperation("获取流程定义列表")
    public Map<String, Object> getProcessDefinitions(
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getProcessDefinitions(pageable);
    }

    @GetMapping("/instances")
    @ApiOperation("获取流程实例列表")
    public Map<String, Object> getProcessInstances(
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getProcessInstances(pageable);
    }

    @GetMapping("/tasks/assigned/{userId}")
    @ApiOperation("获取指定用户的任务列表")
    public Map<String, Object> getAssignedTasks(
            @ApiParam("用户ID") @PathVariable String userId,
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getAssignedTasks(userId, pageable);
    }

    @GetMapping("/tasks/candidate/{userId}")
    @ApiOperation("获取候选任务列表")
    public Map<String, Object> getCandidateTasks(
            @ApiParam("用户ID") @PathVariable String userId,
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getCandidateTasks(userId, pageable);
    }

    @GetMapping("/health")
    @ApiOperation("ERP工作流健康检查")
    public Map<String, Object> healthCheck() {
        log.info("ERP工作流健康检查请求");
        // 委托给主工作流控制器的健康检查
        return workflowService.getProcessDefinitions(PageRequest.of(0, 1));
    }
}
