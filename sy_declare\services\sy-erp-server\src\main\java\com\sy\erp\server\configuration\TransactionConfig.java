package com.sy.erp.server.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class TransactionConfig {
    @Bean(name = "aimoProdMysqlDataSourceTransactionManager")
    public DataSourceTransactionManager aimoProdMysqlDataSourceTransactionManager(
            @Autowired @Qualifier("aimoProdMysql") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "aimoTestMysqlDataSourceTransactionManager")
    public DataSourceTransactionManager aimoTestMysqlDataSourceTransactionManager(
            @Autowired @Qualifier("aimoTestMysql") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "a2019SqlserverDataSourceTransactionManager")
    public DataSourceTransactionManager a2019SqlserverDataSourceTransactionManager(
            @Autowired @Qualifier("a2019Sqlserver") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "xw2020SqlserverDataSourceTransactionManager")
    public DataSourceTransactionManager xw2020SqlserverDataSourceTransactionManager(
            @Autowired @Qualifier("xw2020Sqlserver") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "lpimsoftmesSqlserverDataSourceTransactionManager")
    public DataSourceTransactionManager lpimsoftmesSqlserverDataSourceTransactionManager(
            @Autowired @Qualifier("lpimsoftmesSqlserver") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "transactionManager")
    public DataSourceTransactionManager transactionManager(
            @Autowired @Qualifier("dataSource") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

}
