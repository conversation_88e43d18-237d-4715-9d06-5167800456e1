package com.aimo.base.server.service.prediction.impl;

import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.model.basf.BaseProduct;
import com.aimo.base.client.model.basf.BaseShop;
import com.aimo.base.client.model.prediction.PredictionStockLock;
import com.aimo.base.client.model.prediction.PredictionVersionAsin;
import com.aimo.base.client.model.prediction.SalePrediction;
import com.aimo.base.client.param.salePrediction.PredictionStockLockParam;
import com.aimo.base.client.param.salePrediction.SalePredictionParam;
import com.aimo.base.server.mapper.prediction.PredictionStockLockMapper;
import com.aimo.base.server.service.base.BaseUserService;
import com.aimo.base.server.service.basf.BaseProductService;
import com.aimo.base.server.service.basf.BaseShopService;
import com.aimo.base.server.service.prediction.PredictionStockLockService;
import com.aimo.base.server.service.prediction.SalePredictionService;
import com.aimo.common.exception.OpenAlertException;
import com.aimo.common.mybatis.base.entity.IdEntity;
import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.DateUtils;
import com.aimo.common.utils.ExcelUtil;
import com.aimo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class PredictionStockLockServiceImpl extends BaseServiceImpl<PredictionStockLockMapper, PredictionStockLock> implements PredictionStockLockService {
    @Resource
    private PredictionStockLockMapper stockLockMapper;
    @Resource
    private BaseShopService baseShopService;
    @Resource
    private SalePredictionService salePredictionService;
    @Resource
    private PredictionMonthPurchaseServiceImpl monthPurchaseService;
    @Resource
    private BaseProductService baseProductService;
    @Resource
    private PredictionStockLockPort stockLockPort;
    @Resource
    private BaseUserService baseUserService;

    @Override
    public IPage<PredictionStockLock> listPage(PredictionStockLockParam param) {
        Long userId = OpenHelper.getUserId();
        if(userId != null){
            param.setCreateUser(baseUserService.getUserById(userId).isSuper() ? null : userId);
        }
        return stockLockMapper.selectPages(new Page<>(param.getPage(), param.getLimit()), param);
    }

    @Override
    public void exportFile(HttpServletResponse response, PredictionStockLockParam param) {
        try {
            Workbook workbook = stockLockPort.exportFile(param);
            ExcelUtil.export(response, workbook, "发货库存锁定_" + DateUtils.getCurrentDate().getTime() + ".xls");
        } catch (Exception e) {
            throw new OpenAlertException("导出发货库存锁定失败");
        }
    }

    @Override
    public void updateLockQty(Long id, String itemNo, Integer qty) {
        BaseProduct baseProduct = baseProductService.getBy(null, itemNo);
        if (baseProduct == null) {
            return;
        }
        Long userId = OpenHelper.getUserId();
        String userName = OpenHelper.getNickName();
        if (qty > 0) {
            AtomicInteger atomicInteger = new AtomicInteger(qty);
            QueryWrapper<PredictionStockLock> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(PredictionStockLock::getProductId, baseProduct.getId()).eq(PredictionStockLock::getStatus, BaseConstants.STATUS0).isNotNull(PredictionStockLock::getYsId);
            stockLockMapper.selectList(queryWrapper).forEach(item -> {
                if (atomicInteger.get() <= 0) {
                    return;
                }
                int subQty = Math.min(item.getLockQty(), atomicInteger.get());
                item.setLockQty(item.getLockQty() - subQty);
                if (subQty > 0) {
                    String json = item.getJson();
                    Map<String, Integer> jsonMap = StringUtils.isEmpty(json) ? new HashMap<>() : JSON.parseObject(json, new TypeToken<Map<String, Integer>>() {
                    }.getType());
                    jsonMap.put(StringUtils.toMyString(id), subQty);
                    item.setJson(JSON.toJSONString(jsonMap));
                }
                if (item.getLockQty() <= 0) {
                    item.setStatus(BaseConstants.STATUS1);
                }
                stockLockMapper.updateById(item);
                this.sendSerialMessage(userId, userName, "StockLockSyncYsOthOutOrderAction", "{\"id\":\"" + item.getId() + "\"}");
                atomicInteger.set(atomicInteger.get() - subQty);
            });
        } else {
            QueryWrapper<PredictionStockLock> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(PredictionStockLock::getProductId, baseProduct.getId()).like(PredictionStockLock::getJson, "\"" + id + "\":");
            stockLockMapper.selectList(queryWrapper).forEach(item -> {
                String json = item.getJson();
                if (StringUtils.isEmpty(json)) {
                    return;
                }
                Map<String, Integer> jsonMap = JSON.parseObject(json, new TypeToken<Map<String, Integer>>() {
                }.getType());
                int subQty = jsonMap.remove(StringUtils.toMyString(id));
                item.setJson(JSON.toJSONString(jsonMap));
                item.setLockQty(item.getLockQty() + subQty);
                item.setStatus(BaseConstants.STATUS0);
                stockLockMapper.updateById(item);
                this.sendSerialMessage(userId, userName, "StockLockSyncYsOthOutOrderAction", "{\"id\":\"" + item.getId() + "\"}");
            });
        }
    }

    @Override
    public Boolean importFile(MultipartFile importFile) {
        List<PredictionStockLock> stockLockList = stockLockPort.readStockLock(importFile);
        checkData(stockLockList);
        stockLockList.forEach(this::saveStockLock);
        return true;
    }

    private void checkData(List<PredictionStockLock> stockLockList) {
        if (CollectionUtils.isEmpty(stockLockList)) {
            return;
        }
        SalePredictionParam saleParam = new SalePredictionParam();
        saleParam.setLimit(-1);
        Map<String, Long> salePredictionIdMap = salePredictionService.listSelectPage(saleParam).getRecords().stream().collect(Collectors.toMap(SalePrediction::getKey, SalePrediction::getId, (o1, o2) -> o1));
        List<String> productCodeList = stockLockList.stream().map(PredictionStockLock::getProductCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, Long> productIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productCodeList)) {
            QueryWrapper<BaseProduct> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(BaseProduct::getCode, productCodeList);
            productIdMap.putAll(baseProductService.list(queryWrapper).stream().collect(Collectors.toMap(BaseProduct::getCode, BaseProduct::getId)));
        }

        Map<String, Long> shopIdMap = baseShopService.list().stream().collect(Collectors.toMap(BaseShop::getName, IdEntity::getId));
        Map<String, Long> versionAsinIdMap = new HashMap<>();
        stockLockList.forEach(item -> {
            item.setQty(item.getLockQty());
            PredictionVersionAsin versionAsin = item.getVersionAsin();
            if (versionAsin != null) {
                item.setParentId(monthPurchaseService.getVersionAsinId(versionAsin, shopIdMap, salePredictionIdMap, versionAsinIdMap));
                if (item.getParentId() == null) {
                    throw new OpenAlertException(versionAsin.getKey() + "销售预测不存在");
                }
            } else {
                item.setProductId(productIdMap.get(item.getProductCode()));
                if (item.getProductId() == null) {
                    throw new OpenAlertException(item.getProductCode() + "物料编码不存在");
                }
            }
        });
    }

    private void saveStockLock(PredictionStockLock predictionStockLock) {
        stockLockMapper.insert(predictionStockLock);
    }

    @Override
    public Boolean remove(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        UpdateWrapper<PredictionStockLock> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(PredictionStockLock::getId, idList)
                .set(PredictionStockLock::getStatus, BaseConstants.STATUS1)
                .set(PredictionStockLock::getJson, null)
                .set(PredictionStockLock::getUpdateTime, DateUtils.getCurrentDate()).set(PredictionStockLock::getUpdateUser, OpenHelper.getUserId());
        return stockLockMapper.update(null, updateWrapper) > 0;
    }
}
