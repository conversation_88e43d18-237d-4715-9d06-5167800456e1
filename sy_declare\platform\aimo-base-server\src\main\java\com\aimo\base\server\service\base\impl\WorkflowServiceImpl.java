package com.aimo.base.server.service.base.impl;

import com.aimo.base.client.model.base.BaseUser;
import com.aimo.base.client.model.base.Workflow;
import com.aimo.base.client.service.IBaseUserServiceClient;
import com.aimo.base.server.mapper.base.WorkflowMapper;
import com.aimo.base.server.service.base.WorkflowService;
import com.aimo.common.model.ResultBody;
import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.model.PageParams;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 工作流 服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Slf4j
@Service
public class WorkflowServiceImpl extends BaseServiceImpl<WorkflowMapper, Workflow> implements WorkflowService {

    @Autowired
    private IBaseUserServiceClient baseUserServiceClient;

    @Autowired
    private FastFileStorageClient fastFileStorageClient;

    @Override
    public ResultBody<IPage<Workflow>> getWorkflowPage(Integer page, Integer size, String workflowName, Integer execute) {
        LambdaQueryWrapper<Workflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Workflow::getStatus, 0)
                .like(StringUtils.isNotBlank(workflowName), Workflow::getWorkflowName, workflowName)
                .eq(execute != null, Workflow::getExecute, execute)
                .orderByDesc(Workflow::getCreateTime);

        IPage<Workflow> pageResult = this.page(new Page<>(page, size), queryWrapper);

        // 填充创建人和更新人姓名
        fillUserNames(pageResult.getRecords());

        return ResultBody.ok(pageResult);
    }

    @Override
    public ResultBody<Workflow> getWorkflowById(Long id) {
        Workflow workflow = this.getById(id);
        return ResultBody.ok(workflow);
    }

    @Override
    public ResultBody<String> addWorkflow(Workflow workflow) {
        try {
            // 参数校验
            if (StringUtils.isBlank(workflow.getWorkflowKey())) {
                return ResultBody.failed().msg("工作流Key不能为空");
            }
            if (StringUtils.isBlank(workflow.getWorkflowName())) {
                return ResultBody.failed().msg("工作流名称不能为空");
            }

            // 检查工作流Key是否已存在
            if (existsByWorkflowKey(workflow.getWorkflowKey(), null)) {
                return ResultBody.failed().msg("工作流Key已存在");
            }

            // 设置默认值
            if (workflow.getExecute() == null) {
                workflow.setExecute(1); // 默认启用
            }
            workflow.setStatus(0); // 正常状态
            workflow.setDeployStatus(0); // 默认未部署

            boolean result = this.save(workflow);
            if (result) {
                return ResultBody.ok().msg("新增工作流成功");
            } else {
                return ResultBody.failed().msg("新增工作流失败");
            }
        } catch (Exception e) {
            log.error("新增工作流失败", e);
            return ResultBody.failed().msg("新增工作流失败：" + e.getMessage());
        }
    }

    @Override
    public ResultBody<String> updateWorkflowWithResult(Workflow workflow) {
        try {
            if (workflow.getId() == null) {
                return ResultBody.failed().msg("工作流ID不能为空");
            }

            // 检查记录是否存在
            Workflow existingWorkflow = this.getById(workflow.getId());
            if (existingWorkflow == null || existingWorkflow.getStatus() == 1) {
                return ResultBody.failed().msg("工作流不存在");
            }

            // 参数校验
            if (StringUtils.isNotBlank(workflow.getWorkflowKey())) {
                // 检查工作流Key是否已被其他记录使用
                if (existsByWorkflowKey(workflow.getWorkflowKey(), workflow.getId())) {
                    return ResultBody.failed().msg("工作流Key已存在");
                }
            }
            workflow.setDeployStatus(0);
            boolean result = this.updateById(workflow);
            if (result) {
                return ResultBody.ok().msg("更新工作流成功");
            } else {
                return ResultBody.failed().msg("更新工作流失败");
            }
        } catch (Exception e) {
            log.error("更新工作流失败", e);
            return ResultBody.failed().msg("更新工作流失败：" + e.getMessage());
        }
    }

    @Override
    public ResultBody<String> deleteWorkflowWithResult(Long id) {
        try {
            if (id == null) {
                return ResultBody.failed().msg("工作流ID不能为空");
            }

            // 检查记录是否存在
            Workflow workflow = this.getById(id);
            if (workflow == null || workflow.getStatus() == 1) {
                return ResultBody.failed().msg("工作流不存在");
            }

            // 逻辑删除
            workflow.setStatus(1);

            boolean result = this.updateById(workflow);
            if (result) {
                return ResultBody.ok().msg("删除工作流成功");
            } else {
                return ResultBody.failed().msg("删除工作流失败");
            }
        } catch (Exception e) {
            log.error("删除工作流失败", e);
            return ResultBody.failed().msg("删除工作流失败：" + e.getMessage());
        }
    }

    @Override
    public boolean existsByWorkflowKey(String workflowKey, Long excludeId) {
        if (StringUtils.isBlank(workflowKey)) {
            return false;
        }
        
        LambdaQueryWrapper<Workflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Workflow::getWorkflowKey, workflowKey)
                .eq(Workflow::getStatus, 0);

        if (excludeId != null) {
            queryWrapper.ne(Workflow::getId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public ResultBody<String> saveBpmnProcess(Workflow workflow) {
        try {
            // 参数校验
            if (StringUtils.isBlank(workflow.getWorkflowName())) {
                return ResultBody.failed().msg("工作流名称不能为空");
            }
            if (StringUtils.isBlank(workflow.getWorkflowKey())) {
                return ResultBody.failed().msg("工作流Key不能为空");
            }

            // 检查工作流Key是否已存在
            if (existsByWorkflowKey(workflow.getWorkflowKey(), null)) {
                return ResultBody.failed().msg("工作流Key已存在");
            }

            workflow.setBpmnXml(workflow.getBpmnXml());

            // 设置默认值
            workflow.setExecute(1); // 默认启用
            workflow.setStatus(0); // 正常状态

            boolean result = this.save(workflow);
            if (result) {
                return ResultBody.ok().msg("保存BPMN流程成功").data(workflow.getWorkflowKey());
            } else {
                return ResultBody.failed().msg("保存BPMN流程失败");
            }
        } catch (Exception e) {
            log.error("保存BPMN流程失败", e);
            return ResultBody.failed().msg("保存BPMN流程失败：" + e.getMessage());
        }
    }

    @Override
    public ResultBody<String> uploadBpmnFile(String workflowKey, String bpmnXml) {
        try {
            // 参数校验
            if (StringUtils.isBlank(workflowKey)) {
                return ResultBody.failed().msg("工作流Key不能为空");
            }
            if (StringUtils.isBlank(bpmnXml)) {
                return ResultBody.failed().msg("BPMN内容不能为空");
            }

            // 生成BPMN文件并上传到FDFS
            String fileName = workflowKey + "_" + UUID.randomUUID().toString() + ".bpmn";
            byte[] bpmnBytes = bpmnXml.getBytes("UTF-8");

            // 上传文件到FDFS
            String bpmnUrl = "/" + fastFileStorageClient.uploadFile(
                    new ByteArrayInputStream(bpmnBytes),
                    bpmnBytes.length,
                    "bpmn",
                    null
            ).getFullPath();

            return ResultBody.ok().msg("BPMN文件上传成功").data(bpmnUrl);
        } catch (Exception e) {
            log.error("上传BPMN文件失败", e);
            return ResultBody.failed().msg("上传BPMN文件失败：" + e.getMessage());
        }
    }

    /**
     * 填充用户名称
     */
    private void fillUserNames(List<Workflow> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 收集所有用户ID
            HashSet<Long> userIds = new HashSet<>();
            for (Workflow item : list) {
                if (item.getCreateUser() != null) {
                    userIds.add(item.getCreateUser());
                }
                if (item.getUpdateUser() != null) {
                    userIds.add(item.getUpdateUser());
                }
            }

            if (userIds.isEmpty()) {
                return;
            }

            // 批量查询用户信息
            ResultBody<List<BaseUser>> userResult = baseUserServiceClient.listByIds(userIds);
            if (userResult != null && userResult.getData() != null) {
                Map<Long, String> userIdToNameMap = userResult.getData().stream()
                        .collect(Collectors.toMap(BaseUser::getId, BaseUser::getNickName, (v1, v2) -> v1));

                // 填充用户名称
                for (Workflow item : list) {
                    if (item.getCreateUser() != null) {
                        item.setCreateUserName(userIdToNameMap.get(item.getCreateUser()));
                    }
                    if (item.getUpdateUser() != null) {
                        item.setUpdateUserName(userIdToNameMap.get(item.getUpdateUser()));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("填充用户名称失败: {}", e.getMessage());
        }
    }

    // 实现新接口中的方法

    @Override
    public IPage<Workflow> listPage(PageParams<Workflow> pageParams) {
        Workflow query = pageParams.mapToObject(Workflow.class);
        QueryWrapper<Workflow> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .like(StringUtils.isNotEmpty(query.getWorkflowName()), Workflow::getWorkflowName, query.getWorkflowName())
                .like(StringUtils.isNotEmpty(query.getWorkflowKey()), Workflow::getWorkflowKey, query.getWorkflowKey())
                .eq(query.getExecute() != null, Workflow::getExecute, query.getExecute())
                .eq(query.getDeployStatus() != null, Workflow::getDeployStatus, query.getDeployStatus())
                .eq(Workflow::getStatus, 0); // 只查询正常状态的记录
        queryWrapper.orderByDesc("create_time");
        return this.page(pageParams, queryWrapper);
    }

    @Override
    public Workflow getByWorkflowKey(String workflowKey) {
        if (StringUtils.isEmpty(workflowKey)) {
            return null;
        }
        QueryWrapper<Workflow> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Workflow::getWorkflowKey, workflowKey)
                .eq(Workflow::getStatus, 0);
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean checkWorkflowKey(String workflowKey) {
        return existsByWorkflowKey(workflowKey, null);
    }

    @Override
    public boolean saveWorkflow(Workflow workflow) {
        if (workflow == null) {
            return false;
        }
        workflow.setStatus(0); // 正常状态
        workflow.setDeployStatus(0); // 未部署
        if (workflow.getExecute() == null) {
            workflow.setExecute(1); // 默认启用
        }
        return this.save(workflow);
    }

    @Override
    public boolean updateWorkflow(Workflow workflow) {
        if (workflow == null || workflow.getId() == null) {
            return false;
        }
        return this.updateById(workflow);
    }

    @Override
    public List<Workflow> getEnabledWorkflows() {
        QueryWrapper<Workflow> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Workflow::getStatus, 0)
                .eq(Workflow::getExecute, 1);
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }

    @Override
    public boolean deployWorkflow(Long id) {
        if (id == null) {
            return false;
        }
        Workflow workflow = new Workflow();
        workflow.setId(id);
        workflow.setDeployStatus(1); // 已部署
        return this.updateById(workflow);
    }

    @Override
    public boolean undeployWorkflow(Long id) {
        if (id == null) {
            return false;
        }
        Workflow workflow = new Workflow();
        workflow.setId(id);
        workflow.setDeployStatus(0); // 未部署
        return this.updateById(workflow);
    }

    @Override
    public boolean deleteWorkflow(Long id) {
        if (id == null) {
            return false;
        }
        Workflow workflow = new Workflow();
        workflow.setId(id);
        workflow.setStatus(1); // 删除状态
        return this.updateById(workflow);
    }
}
