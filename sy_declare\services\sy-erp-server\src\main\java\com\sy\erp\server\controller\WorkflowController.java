package com.sy.erp.server.controller;

import com.aimo.base.client.model.base.Workflow;
import com.sy.erp.server.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.RepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流控制器
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@RestController
@RequestMapping("/workflow")
@Api(tags = "工作流管理")
public class WorkflowController {

    @Autowired
    private WorkflowService workflowService;

    @Autowired(required = false)
    @Qualifier("workflowSafeProcessEngine")
    private ProcessEngine processEngine;

    @Autowired(required = false)
    @Qualifier("workflowSafeRepositoryService")
    private RepositoryService repositoryService;

    @GetMapping("/health")
    @ApiOperation("工作流健康检查")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (processEngine != null) {
                result.put("success", true);
                result.put("engineName", processEngine.getName());
                result.put("engineVersion", ProcessEngine.VERSION);
                result.put("message", "工作流引擎运行正常");
                
                if (repositoryService != null) {
                    long count = repositoryService.createProcessDefinitionQuery().count();
                    result.put("processDefinitionCount", count);
                } else {
                    result.put("repositoryService", "不可用");
                }
            } else {
                result.put("success", false);
                result.put("message", "工作流引擎未初始化");
            }
        } catch (Exception e) {
            log.error("工作流健康检查失败", e);
            result.put("success", false);
            result.put("message", "健康检查失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    @PostMapping("/deploy")
    @ApiOperation("部署流程定义")
    public Map<String, Object> deployProcess(@RequestBody Workflow workflow) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (workflow == null) {
                result.put("success", false);
                result.put("message", "工作流对象不能为空");
                return result;
            }

            if (workflow.getBpmnXml() == null || workflow.getBpmnXml().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "BPMN XML内容不能为空");
                return result;
            }

            if (workflow.getWorkflowName() == null || workflow.getWorkflowName().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "工作流名称不能为空");
                return result;
            }

            if (workflow.getWorkflowKey() == null || workflow.getWorkflowKey().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "工作流Key不能为空");
                return result;
            }

            // 调用工作流服务部署流程
            Map<String, Object> deployResult = workflowService.deployProcess(
                workflow.getWorkflowName(),
                workflow.getWorkflowKey(),
                workflow.getBpmnXml()
            );

            return deployResult;
        } catch (Exception e) {
            log.error("部署工作流失败", e);
            result.put("success", false);
            result.put("message", "部署工作流失败: " + e.getMessage());
            return result;
        }
    }

    @DeleteMapping("/deployment/{deploymentId}")
    @ApiOperation("删除部署")
    public Map<String, Object> deleteDeployment(
            @ApiParam("部署ID") @PathVariable String deploymentId,
            @ApiParam("是否级联删除") @RequestParam(defaultValue = "false") boolean cascade) {
        return workflowService.deleteDeployment(deploymentId, cascade);
    }

    @PostMapping("/start")
    @ApiOperation("启动流程实例")
    public Map<String, Object> startProcess(
            @ApiParam("流程定义Key") @RequestParam String processDefinitionKey,
            @ApiParam("业务Key") @RequestParam(required = false) String businessKey,
            @ApiParam("流程变量") @RequestBody(required = false) Map<String, Object> variables) {
        return workflowService.startProcess(processDefinitionKey, businessKey, variables);
    }

    @PostMapping("/task/{taskId}/complete")
    @ApiOperation("完成任务")
    public Map<String, Object> completeTask(
            @ApiParam("任务ID") @PathVariable String taskId,
            @ApiParam("任务变量") @RequestBody(required = false) Map<String, Object> variables) {
        return workflowService.completeTask(taskId, variables);
    }

    @GetMapping("/definitions")
    @ApiOperation("获取流程定义列表")
    public Map<String, Object> getProcessDefinitions(
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getProcessDefinitions(pageable);
    }

    @GetMapping("/instances")
    @ApiOperation("获取流程实例列表")
    public Map<String, Object> getProcessInstances(
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getProcessInstances(pageable);
    }

    @GetMapping("/tasks/user/{userId}")
    @ApiOperation("获取用户任务列表")
    public Map<String, Object> getUserTasks(
            @ApiParam("用户ID") @PathVariable String userId,
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getUserTasks(userId, pageable);
    }

    @GetMapping("/tasks/candidate/{userId}")
    @ApiOperation("获取候选任务列表")
    public Map<String, Object> getCandidateTasks(
            @ApiParam("用户ID") @PathVariable String userId,
            @ApiParam("页码") @RequestParam(defaultValue = "0") int page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        return workflowService.getCandidateTasks(userId, pageable);
    }

    @GetMapping("/instance/{processInstanceId}/diagram")
    @ApiOperation("获取流程图")
    public Map<String, Object> getProcessDiagram(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        return workflowService.getProcessDiagram(processInstanceId);
    }

    @GetMapping("/instance/{processInstanceId}/history")
    @ApiOperation("获取流程历史")
    public Map<String, Object> getProcessHistory(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        return workflowService.getProcessHistory(processInstanceId);
    }

    @PostMapping("/definition/{processDefinitionId}/suspend")
    @ApiOperation("挂起流程定义")
    public Map<String, Object> suspendProcessDefinition(
            @ApiParam("流程定义ID") @PathVariable String processDefinitionId) {
        return workflowService.suspendProcessDefinition(processDefinitionId);
    }

    @PostMapping("/definition/{processDefinitionId}/activate")
    @ApiOperation("激活流程定义")
    public Map<String, Object> activateProcessDefinition(
            @ApiParam("流程定义ID") @PathVariable String processDefinitionId) {
        return workflowService.activateProcessDefinition(processDefinitionId);
    }

    @GetMapping("/instance/{processInstanceId}/variables")
    @ApiOperation("获取流程变量")
    public Map<String, Object> getProcessVariables(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        return workflowService.getProcessVariables(processInstanceId);
    }

    @PostMapping("/instance/{processInstanceId}/variables")
    @ApiOperation("设置流程变量")
    public Map<String, Object> setProcessVariables(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId,
            @ApiParam("流程变量") @RequestBody Map<String, Object> variables) {
        return workflowService.setProcessVariables(processInstanceId, variables);
    }

    @PostMapping("/test/deploy-simple")
    @ApiOperation("部署简单测试流程")
    public Map<String, Object> deploySimpleTestProcess() {
        String bpmnXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" \n" +
                "             xmlns:activiti=\"http://activiti.org/bpmn\" \n" +
                "             targetNamespace=\"http://www.activiti.org/test\">\n" +
                "  <process id=\"simpleTest\" name=\"简单测试流程\" isExecutable=\"true\">\n" +
                "    <startEvent id=\"start\" name=\"开始\"></startEvent>\n" +
                "    <userTask id=\"task\" name=\"测试任务\" activiti:assignee=\"test\">\n" +
                "      <documentation>这是一个测试任务</documentation>\n" +
                "    </userTask>\n" +
                "    <endEvent id=\"end\" name=\"结束\"></endEvent>\n" +
                "    <sequenceFlow id=\"flow1\" sourceRef=\"start\" targetRef=\"task\"></sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow2\" sourceRef=\"task\" targetRef=\"end\"></sequenceFlow>\n" +
                "  </process>\n" +
                "</definitions>";

        return workflowService.deployProcess("简单测试流程", "simpleTestWorkflow", bpmnXml);
    }

    @PostMapping("/test/start-simple")
    @ApiOperation("启动简单测试流程")
    public Map<String, Object> startSimpleTestProcess() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("testVar", "测试变量值");
        variables.put("startTime", System.currentTimeMillis());
        
        return workflowService.startProcess("simpleTest", "test-" + System.currentTimeMillis(), variables);
    }
}
