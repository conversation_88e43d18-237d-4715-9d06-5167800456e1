<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aimo.base.server.mapper.base.WorkflowMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.aimo.base.client.model.base.Workflow">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="workflow_key" property="workflowKey" jdbcType="VARCHAR"/>
        <result column="workflow_name" property="workflowName" jdbcType="VARCHAR"/>
        <result column="bpmn_xml" property="bpmnXml" jdbcType="LONGVARCHAR"/>
        <result column="execute" property="execute" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="deploy_status" property="deployStatus" jdbcType="TINYINT"/>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, workflow_key, workflow_name, bpmn_xml, execute, create_user, create_time,
        update_user, update_time, `status`, deploy_status
    </sql>
    
</mapper>
