spring:
  datasource:
    # 主数据源配置
    url: **************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 动态数据源配置
    aimo:
      prod:
        mysql:
          jdbc-url: ************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
      test:
        mysql:
          jdbc-url: ************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
    
    a2019:
      sqlserver:
        jdbc-url: ****************************************************************
        username: sa
        password: sa123
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    
    xw2020:
      sqlserver:
        jdbc-url: *****************************************************************
        username: sa
        password: sa123
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    
    lpimsoftmes:
      sqlserver:
        jdbc-url: **********************************************************************
        username: sa
        password: sa123
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

  cloud:
    nacos:
      config:
        enabled: false  # 禁用Nacos配置，使用本地配置

# 日志配置
logging:
  level:
    com.sy.erp: DEBUG
    org.springframework.jdbc: DEBUG
    com.zaxxer.hikari: DEBUG
