package com.sy.ys.server.sync.inventory;

import com.aimo.common.mybatis.base.service.IBaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sy.ys.client.model.inventory.YsInventory;
import com.sy.ys.client.param.inventory.InventoryParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface InventorySyncService{
    boolean syncYsInventory(List<String> codeList);


    Map<Long,Map<Long,Integer>> loadWarehouseLoc(List<Long> idList);
}
