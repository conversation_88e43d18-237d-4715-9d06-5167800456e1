package com.sy.ys.server.sync.inventory.impl;

import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sy.ys.client.model.base.YsOrg;
import com.sy.ys.client.model.base.YsProduct;
import com.sy.ys.client.model.base.YsWarehouse;
import com.sy.ys.client.model.inventory.YsInventory;
import com.sy.ys.server.feign.base.BaseClient;
import com.sy.ys.server.feign.base.BaseClientComp;
import com.sy.ys.server.feign.model.inventory.YSDataInventory;
import com.sy.ys.server.feign.model.inventory.YSDataInventoryLoc;
import com.sy.ys.server.service.base.OrgBaseService;
import com.sy.ys.server.service.base.ProductBaseService;
import com.sy.ys.server.service.base.WarehouseService;
import com.sy.ys.server.service.inventory.InventoryService;
import com.sy.ys.server.sync.inventory.InventorySyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InventorySyncServiceImpl implements InventorySyncService {
    public final static String INVENTORY_LIST_URL = "/yonbip/scm/stockanalysis/list";

    public final static String INVENTORY_LOC_LIST_URL = "/yonbip/scm/locationstockanalysis/list";
    @Resource
    private OrgBaseService orgBaseService;
    @Resource
    private ProductBaseService productBaseService;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private BaseClientComp baseClientComp;

    @Resource
    private InventoryService inventoryService;

    @Override
    public boolean syncYsInventory(List<String> codeList) {
        YsOrg ysOrg = orgBaseService.getByCode("A01");
        if (ysOrg == null) {
            return false;
        }
        QueryWrapper<YsWarehouse> warehouseQueryWrapper = new QueryWrapper<>();
        warehouseQueryWrapper.lambda().eq(YsWarehouse::getOrgId, ysOrg.getId()).in(YsWarehouse::getName, Arrays.asList("良品仓", "不良品仓"));
        warehouseService.list(warehouseQueryWrapper).forEach(warehouse -> {
            Map<Long, YsInventory> inventoryMap = new HashMap<>();
            List<YSDataInventory> downList = downList(codeList, warehouse.getId());
            boolean isGoodWarehouse = StringUtils.equals(warehouse.getName(), "良品仓");
            downList.forEach(item -> {
                YsInventory ysInventory = inventoryMap.get(item.getProduct());
                if (ysInventory == null) {
                    ysInventory = new YsInventory(item.getProduct());
                    inventoryMap.put(item.getProduct(), ysInventory);
                }
                int qty = StringUtils.toBigDecimal(item.getAvailableqty(), BigDecimal.ZERO).intValue();
                if (isGoodWarehouse) {
                    setQty(ysInventory, qty);
                } else {
                    ysInventory.setBadQty(qty + ysInventory.getBadQty());
                }
            });
            Map<Long, YsInventory> existMap = inventoryService.list().stream().collect(Collectors.toMap(YsInventory::getProductId, Function.identity(), (o1, o2) -> o1));
            inventoryMap.forEach((productId, inventory) -> {
                YsInventory existInventory = existMap.get(productId);
                if (existInventory == null) {
                    inventoryService.save(inventory);
                    return;
                }
                if (isGoodWarehouse) {
                    existInventory.setLockQty(inventory.getLockQty());
                    existInventory.setQty(inventory.getQty());
                } else {
                    existInventory.setBadQty(inventory.getBadQty());
                }
                inventoryService.updateById(existInventory);
            });
            if (isGoodWarehouse) {
                List<Long> idList = inventoryMap.values().stream().filter(item -> item.getQty() > 0).map(YsInventory::getId).collect(Collectors.toList());
                QueryWrapper<YsInventory> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().gt(YsInventory::getQty, 0).notIn(!CollectionUtils.isEmpty(idList), YsInventory::getId, idList);
                List<YsInventory> ysInventoryList = inventoryService.list(queryWrapper);

                idList = inventoryMap.values().stream().filter(item -> item.getLockQty() > 0).map(YsInventory::getId).collect(Collectors.toList());
                queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().gt(YsInventory::getLockQty, 0).notIn(!CollectionUtils.isEmpty(idList), YsInventory::getId, idList);
                ysInventoryList.addAll(inventoryService.list(queryWrapper));

                new HashSet<>(ysInventoryList).forEach(item -> {
                    try {
                        YsProduct ysProduct = productBaseService.getById(item.getProductId());
                        if (ysProduct == null) {
                            item.setQty(0);
                            item.setLockQty(0);
                        } else {
                            List<YSDataInventory> singleList = downList(Collections.singletonList(ysProduct.getCode()), warehouse.getId());
                            if (CollectionUtils.isEmpty(singleList)) {
                                item.setQty(0);
                                item.setLockQty(0);
                            } else {
                                item.setQty(singleList.stream().mapToInt(each -> StringUtils.toBigDecimal(each.getAvailableqty(), BigDecimal.ZERO).intValue()).filter(each -> each > 0).sum());
                                item.setLockQty(-singleList.stream().mapToInt(each -> StringUtils.toBigDecimal(each.getAvailableqty(), BigDecimal.ZERO).intValue()).filter(each -> each < 0).sum());
                            }
                        }
                    } catch (Exception e) {
                        item.setBadQty(0);
                        item.setLockQty(0);
                    }
                    inventoryService.updateById(item);
                });
            } else {
                List<Long> idList = inventoryMap.values().stream().filter(item -> item.getBadQty() > 0).map(YsInventory::getId).collect(Collectors.toList());
                QueryWrapper<YsInventory> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().gt(YsInventory::getBadQty, 0).notIn(!CollectionUtils.isEmpty(idList), YsInventory::getId, idList);
                inventoryService.list(queryWrapper).forEach(item -> {
                    try {
                        YsProduct ysProduct = productBaseService.getById(item.getProductId());
                        if (ysProduct == null) {
                            item.setBadQty(0);
                        } else {
                            List<YSDataInventory> singleList = downList(Collections.singletonList(ysProduct.getCode()), warehouse.getId());
                            if (CollectionUtils.isEmpty(singleList)) {
                                item.setBadQty(0);
                            } else {
                                item.setBadQty(singleList.stream().mapToInt(each -> StringUtils.toBigDecimal(each.getAvailableqty(), BigDecimal.ZERO).intValue()).sum());
                            }
                        }
                    } catch (Exception e) {
                        item.setBadQty(0);
                    }
                    inventoryService.updateById(item);
                });
            }
        });
        return true;
    }

    private List<YSDataInventory> downList(List<String> codeList, Long warehouseId) {
        Map<String, Object> param = new HashMap<>();
        int page = 1;
        param.put("pageIndex", page);
        param.put("pageSize", BaseClient.defaultPageSize);
        param.put("warehouse_name", warehouseId);
        if (!CollectionUtils.isEmpty(codeList)) {
            param.put("product_cCode", codeList);
        }
        return baseClientComp.downAllListData(param, YSDataInventory.class, INVENTORY_LIST_URL);
    }

    private void setQty(YsInventory ysInventory, int qty) {
        if (qty > 0) {//大于0为库存
            ysInventory.setQty(qty + ysInventory.getQty());
        } else {//小于0为占用
            ysInventory.setLockQty(ysInventory.getLockQty() - qty);
        }
    }
    public Map<Long,Map<Long,Integer>> loadWarehouseLoc(List<Long> idList){
        YsOrg ysOrg = orgBaseService.getByCode("A01");
        if (ysOrg == null) {
            return new HashMap<>();
        }
        YsWarehouse ysWarehouse = warehouseService.listDefaultPO(ysOrg.getId());
        if(ysWarehouse == null){
            return new HashMap<>();
        }
        Map<String, Object> param = new HashMap<>();
        int page = 1;
        param.put("pageIndex", page);
        param.put("pageSize", BaseClient.defaultPageSize);
        param.put("warehouse_name", ysWarehouse.getId());
        if (!CollectionUtils.isEmpty(idList)) {
            param.put("product.cName", idList);
        }
        List<YSDataInventoryLoc> locList =  baseClientComp.downAllListData(param, YSDataInventoryLoc.class, INVENTORY_LOC_LIST_URL);
        Map<Long,Map<Long,Integer>> result = new HashMap<>();
        locList.forEach(item->result.computeIfAbsent(item.getProduct(),k->new HashMap<>()).put(item.getLocation(),StringUtils.toBigDecimal(item.getAvailableqty(),BigDecimal.ZERO).intValue()));
        return result;
    }

}
