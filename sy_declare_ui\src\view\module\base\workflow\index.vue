<template>
  <div class="workflow-list">
    <Card>
      <!-- 搜索区域 -->
      <Form ref="searchForm" :model="searchForm" inline class="search-form">
        <FormItem>
          <Input
            v-model="searchForm.workflowName"
            placeholder="请输入工作流名称"
            style="width: 200px"
            @on-enter="handleSearch"
          />
        </FormItem>
        <FormItem>
          <Select
            v-model="searchForm.execute"
            placeholder="请选择状态"
            style="width: 120px"
            clearable
          >
            <Option :value="1">启用</Option>
            <Option :value="0">停用</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button type="primary" @click="handleSearch">查询</Button>
          <Button @click="handleReset" style="margin-left: 8px">重置</Button>
        </FormItem>
      </Form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <Button type="primary" icon="md-add" @click="handleAdd">新增流程</Button>
        <div class="right-buttons">
          <Button type="warning" icon="md-rocket" @click="handleElementManage" style="margin-right: 8px">上新字段管理</Button>
          <Button type="success" icon="md-settings" @click="handleFunctionManage">流程功能</Button>
        </div>
      </div>

      <!-- 数据表格 -->
      <Table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        stripe
        :max-height="600"
      >
        <template v-slot:status="{ row }">
          <Badge v-if="row.execute === 1" status="success" text="启用" />
          <Badge v-else status="error" text="停用" />
        </template>

        <template v-slot:deployStatus="{ row }">
          <Badge v-if="row.deployStatus === 1" status="success" text="已部署" />
          <Badge v-else status="error" text="未部署" />
        </template>

        <template v-slot:nodeCount="{ row }">
          <Tag color="blue">{{ row.nodeCount || 0 }}个节点</Tag>
        </template>

        <template v-slot:action="{ row }">
          <Button v-if="row.deployStatus === 0" type="error" size="small" @click="handleDeploy(row)" style="margin-right: 4px;">部署</Button>
          <Button type="primary" size="small" @click="handleEdit(row)" style="margin-right: 4px;">编辑</Button>
          <Button
            :type="row.execute === 1 ? 'warning' : 'success'"
            size="small"
            @click="handleToggleStatus(row)"
            style="margin-right: 4px;"
          >
            {{ row.execute === 1 ? '停用' : '启用' }}
          </Button>
          <Button type="error" size="small" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <Page
          :total="pageInfo.total"
          :current="pageInfo.page"
          :page-size="pageInfo.limit"
          show-elevator
          show-sizer
          show-total
          @on-change="handlePageChange"
          @on-page-size-change="handlePageSizeChange"
        />
      </div>
    </Card>

    <!-- 上新字段管理弹窗 -->
    <Modal
      v-model="elementManageModal"
      title="上新字段管理"
      width="800"
      :mask-closable="false"
      @on-cancel="handleElementModalCancel"
    >
      <div class="element-manage-content">
        <!-- 分类标签 -->
        <Tabs v-model="activeElementType" @on-click="handleElementTypeChange">
          <TabPane label="基础信息字段" name="1">
            <div class="element-list">
              <!-- 添加按钮 -->
              <div class="add-element-btn">
                <Button type="dashed" icon="md-add" @click="handleAddElement" long>
                  添加基础信息字段
                </Button>
              </div>
              <!-- 字段列表 -->
              <Table
                :columns="elementColumns"
                :data="basicElementList"
                :loading="false"
                size="small"
                stripe
              ></Table>
            </div>
          </TabPane>
          <TabPane label="标准信息字段" name="2">
            <div class="element-list">
              <!-- 添加按钮 -->
              <div class="add-element-btn">
                <Button type="dashed" icon="md-add" @click="handleAddElement" long>
                  添加标准信息字段
                </Button>
              </div>
              <!-- 字段列表 -->
              <Table
                :columns="elementColumns"
                :data="standardElementList"
                :loading="false"
                size="small"
                stripe
              ></Table>
            </div>
          </TabPane>
        </Tabs>
      </div>
      <div slot="footer">
        <Button @click="handleElementModalCancel">关闭</Button>
      </div>
    </Modal>

    <!-- 添加/编辑字段弹窗 -->
    <Modal
      v-model="elementFormModal"
      :title="elementFormTitle"
      width="500"
      :mask-closable="false"
      @on-cancel="handleElementFormCancel"
    >
      <Form ref="elementForm" :model="elementForm" :rules="elementFormRules" :label-width="100">
        <FormItem label="字段名称" prop="name">
          <Input v-model="elementForm.name" placeholder="请输入字段名称" />
        </FormItem>
        <FormItem label="字段英文" prop="element">
          <Input v-model="elementForm.element" placeholder="请输入字段英文标识" />
        </FormItem>
        <FormItem label="字段类型" prop="type">
          <RadioGroup v-model="elementForm.type">
            <Radio :label="1">基础信息</Radio>
            <Radio :label="2">标准信息</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="handleElementFormCancel">取消</Button>
        <Button type="primary" :loading="elementFormLoading" @click="handleElementFormSubmit">
          {{ elementForm.id ? '更新' : '添加' }}
        </Button>
      </div>
    </Modal>

    <!-- 流程功能管理弹窗 -->
    <Modal v-model="functionModalVisible" title="流程功能管理" width="80%" :mask-closable="false">
      <div class="function-manage">
        <!-- 功能操作按钮 -->
        <div class="function-actions">
          <Button type="primary" icon="md-add" @click="handleFunctionAdd" style="margin-right: 4px;">新增功能</Button>
          <Button icon="md-refresh" @click="loadFunctionData">刷新</Button>
        </div>

        <!-- 功能列表表格 -->
        <Table
          :columns="functionColumns"
          :data="functionData"
          :loading="functionLoading"
          stripe
          :max-height="400"
          style="margin-top: 16px;"
        >
          <template v-slot:status="{ row }">
            <Badge v-if="row.status === 0" status="success" text="正常" />
            <Badge v-else status="error" text="删除" />
          </template>

          <template v-slot:action="{ row }">
            <Button type="primary" size="small" @click="handleFunctionEdit(row)" style="margin-right: 4px;">编辑</Button>
            <Button type="error" size="small" @click="handleFunctionDelete(row)">删除</Button>
          </template>
        </Table>
      </div>

      <div slot="footer">
        <Button @click="functionModalVisible = false">关闭</Button>
      </div>
    </Modal>

    <!-- 功能编辑弹窗 -->
    <Modal v-model="functionEditModalVisible" :title="functionEditMode === 'add' ? '新增功能' : '编辑功能'" @on-ok="handleFunctionSave">
      <Form :model="functionForm" :rules="functionRules" ref="functionForm" :label-width="80">
        <FormItem label="功能KEY" prop="key">
          <Input v-model="functionForm.key" placeholder="请输入功能KEY" :disabled="functionEditMode === 'edit'" />
        </FormItem>
        <FormItem label="功能名称" prop="name">
          <Input v-model="functionForm.name" placeholder="请输入功能名称" />
        </FormItem>
      </Form>
    </Modal>

    <!-- BPMN XML查看弹窗 -->
    <Modal v-model="bpmnViewModalVisible" title="BPMN XML内容" width="80%" :mask-closable="false">
      <div class="bpmn-xml-content">
        <div class="xml-header">
          <span class="workflow-name">{{ currentBpmnWorkflow.workflowName }}</span>
          <Button type="primary" size="small" @click="copyBpmnXml" style="float: right;">
            <Icon type="md-copy" />
            复制XML
          </Button>
        </div>
        <div class="xml-viewer">
          <pre><code>{{ currentBpmnXml }}</code></pre>
        </div>
      </div>
      <div slot="footer">
        <Button @click="bpmnViewModalVisible = false">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import workflowApi from '@/api/base/workflow'
import funElementApi from '@/api/base/funElement'

export default {
  name: 'WorkflowList',
  data() {
    return {
      // 搜索表单
      searchForm: {
        workflowName: '',
        execute: null
      },

      // 表格列定义
      columns: [
        {
          title: '序号',
          type: 'index',
          width: 50,
          align: 'center'
        },
        {
          title: '工作流Key',
          key: 'workflowKey',
          width: 150,
          align: 'center'
        },
        {
          title: '工作流名称',
          key: 'workflowName',
          minWidth: 150
        },
        {
          title: 'BPMN文件',
          key: 'bpmnXml',
          width: 120,
          align: 'center',
          render: (h, params) => {
            if (params.row.bpmnXml) {
              return h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleViewBpmn(params.row)
                  }
                }
              }, '查看文件')
            } else {
              return h('span', '无文件')
            }
          }
        },
        {
          title: '状态',
          key: 'execute',
          width: 80,
          align: 'center',
          slot: 'status'
        },
        {
          title: '部署状态',
          key: 'deployStatus',
          width: 150,
          align: 'center',
          slot: 'deployStatus'
        },
        {
          title: '创建人',
          key: 'createUserName',
          width: 140,
          align: 'center'
        },
        {
          title: '更新人',
          key: 'updateUserName',
          width: 140,
          align: 'center'
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 280,
          align: 'center',
          slot: 'action'
        }
      ],

      // 表格数据
      tableData: [],

      // 加载状态
      loading: false,

      // 分页信息
      pageInfo: {
        page: 1,
        limit: 10,
        total: 0
      },
      currentWorkflow: {},

      // BPMN查看相关
      bpmnViewModalVisible: false,
      currentBpmnWorkflow: {},
      currentBpmnXml: '',

      // 上新字段管理相关数据
      elementManageModal: false,
      activeElementType: '1', // 1: 基础信息, 2: 标准信息
      basicElementList: [],
      standardElementList: [],
      elementFormModal: false,
      elementFormLoading: false,
      elementForm: {
        id: null,
        name: '',
        element: '',
        type: 1,
        funType: 10001 // 上新功能类型枚举
      },
      // 表格列定义
      elementColumns: [
        {
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '字段名称',
          key: 'name',
          minWidth: 120
        },
        {
          title: '字段英文',
          key: 'element',
          minWidth: 150,
          render: (h, params) => {
            return h('code', {
              style: {
                background: '#f5f5f5',
                padding: '2px 6px',
                borderRadius: '3px',
                fontSize: '12px',
                fontFamily: 'Courier New, monospace'
              }
            }, params.row.element)
          }
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 150,
          render: (h, params) => {
            return h('span', params.row.createTime ? params.row.createTime : '-')
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.handleEditElement(params.row)
                  }
                }
              }, [
                '编辑'
              ]),
              h('Button', {
                props: {
                  type: 'error',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDeleteElement(params.row)
                  }
                }
              }, [
                '删除'
              ])
            ])
          }
        }
      ],
      elementFormRules: {
        name: [
          { required: true, message: '请输入字段名称', trigger: 'blur' }
        ],
        element: [
          { required: true, message: '请输入字段英文标识', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段英文必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        type: [
          { required: true, type: 'number', message: '请选择字段类型', trigger: 'change' }
        ]
      },

      // 流程功能管理相关
      functionModalVisible: false,
      functionEditModalVisible: false,
      functionEditMode: 'add', // add | edit
      functionLoading: false,
      functionData: [],
      functionForm: {
        id: null,
        key: '',
        name: ''
      },
      functionRules: {
        key: [
          { required: true, message: '请输入功能KEY', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '功能KEY必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入功能名称', trigger: 'blur' }
        ]
      },
      functionColumns: [
        {
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '功能KEY',
          key: 'key',
          minWidth: 150
        },
        {
          title: '功能名称',
          key: 'name',
          minWidth: 150
        },
        {
          title: '创建人',
          key: 'createUserName',
          width: 140,
          align: 'center'
        },
        {
          title: '更新人',
          key: 'updateUserName',
          width: 140,
          align: 'center'
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          slot: 'action'
        }
      ]
    }
  },

  computed: {
    // 表单标题
    elementFormTitle() {
      return this.elementForm.id ? '编辑字段' : '添加字段'
    }
  },

  mounted() {
    this.loadTableData()
  },

  methods: {
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          page: this.pageInfo.page,
          size: this.pageInfo.limit,
          workflowName: this.searchForm.workflowName,
          execute: this.searchForm.execute
        }

        const response = await workflowApi.getWorkflowPage(params)
        if (response.code === 0) {
          this.tableData = response.data.records || []
          this.pageInfo.total = Number(response.data.total) || 0
        } else {
          this.$Message.error(response.message || '获取工作流列表失败')
        }
      } catch (error) {
        console.error('获取工作流列表失败:', error)
        this.$Message.error('获取工作流列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      console.log('搜索条件:', this.searchForm)
      this.pageInfo.page = 1
      this.loadTableData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        workflowName: '',
        execute: null
      }
      this.handleSearch()
    },

    // 刷新
    handleRefresh() {
      this.loadTableData()
    },

    // 新增流程
    handleAdd() {
      this.$router.push('/base/workflow/add')
    },

    // 查看BPMN文件
    handleViewBpmn(row) {
      if (row.bpmnXml) {
        this.currentBpmnWorkflow = row
        this.currentBpmnXml = row.bpmnXml
        this.bpmnViewModalVisible = true
      } else {
        this.$Message.warning('该工作流暂无BPMN文件')
      }
    },

    // 复制BPMN XML内容
    copyBpmnXml() {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(this.currentBpmnXml).then(() => {
          this.$Message.success('BPMN XML已复制到剪贴板')
        }).catch(() => {
          this.$Message.error('复制失败，请手动复制')
        })
      } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea')
        textArea.value = this.currentBpmnXml
        document.body.appendChild(textArea)
        textArea.select()
        try {
          document.execCommand('copy')
          this.$Message.success('BPMN XML已复制到剪贴板')
        } catch (err) {
          this.$Message.error('复制失败，请手动复制')
        }
        document.body.removeChild(textArea)
      }
    },

    // 编辑工作流定义
    handleEdit(row) {
      this.$router.push(`/base/workflow/add/${row.id}`)
    },

    // 删除工作流定义
    handleDelete(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除"${row.workflowName}"工作流吗？删除后不可恢复。`,
        onOk: async () => {
          try {
            const response = await workflowApi.deleteWorkflow(row.id)
            if (response.code === 0) {
              this.$Message.success(response.message || '工作流删除成功')
              // 重新加载数据
              this.loadTableData()
            } else {
              this.$Message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除工作流失败:', error)
            this.$Message.error('删除失败，请重试')
          }
        }
      })
    },

    // 切换启用/停用状态
    async handleToggleStatus(row) {
      try {
        const newStatus = row.execute === 1 ? 0 : 1
        const statusText = newStatus === 1 ? '启用' : '停用'

        const updateData = {
          id: row.id,
          execute: newStatus
        }

        const response = await workflowApi.updateWorkflow(updateData)
        if (response.code === 0) {
          this.$Message.success(`${statusText}成功`)
          // 更新本地数据
          row.execute = newStatus
        } else {
          this.$Message.error(response.message || `${statusText}失败`)
        }
      } catch (error) {
        console.error('切换状态失败:', error)
        this.$Message.error('操作失败，请重试')
      }
    },

    // ========== 上新字段管理相关方法 ==========

    // 上新字段管理
    handleElementManage() {
      this.elementManageModal = true
      this.activeElementType = '1'
      this.loadElementData()
    },

    // 加载字段数据
    async loadElementData() {
      try {
        const response = await funElementApi.getList({
          funType: 10001 // 上新功能类型
        })

        if (response.code === 0) {
          const allElements = response.data || []
          // 按类型分组
          this.basicElementList = allElements.filter(item => item.type === 1)
          this.standardElementList = allElements.filter(item => item.type === 2)
        } else {
          this.$Message.error(response.message || '加载字段数据失败')
        }
      } catch (error) {
        console.error('加载字段数据失败:', error)
        this.$Message.error('加载字段数据失败')
      }
    },

    // 切换字段类型
    handleElementTypeChange(name) {
      this.activeElementType = name
    },

    // 添加字段
    handleAddElement() {
      this.elementForm = {
        id: null,
        name: '',
        element: '',
        type: parseInt(this.activeElementType),
        funType: 10001
      }
      this.elementFormModal = true

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.elementForm) {
          this.$refs.elementForm.clearValidate()
        }
      })
    },

    // 编辑字段
    handleEditElement(item) {
      this.elementForm = {
        id: item.id,
        name: item.name,
        element: item.element,
        type: item.type,
        funType: item.funType
      }
      this.elementFormModal = true

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.elementForm) {
          this.$refs.elementForm.clearValidate()
        }
      })
    },

    // 删除字段
    handleDeleteElement(item) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除字段"${item.name}"吗？`,
        onOk: async () => {
          try {
            const response = await funElementApi.delete(item.id)
            if (response.code === 0) {
              this.$Message.success('删除成功')
              this.loadElementData()
            } else {
              this.$Message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除字段失败:', error)
            this.$Message.error('删除失败')
          }
        }
      })
    },

    // 关闭字段管理弹窗
    handleElementModalCancel() {
      this.elementManageModal = false
    },

    // 提交字段表单
    async handleElementFormSubmit() {
      console.log('提交表单，当前表单数据:', this.elementForm)
      this.$refs.elementForm.validate(async (valid) => {
        console.log('表单验证结果:', valid)
        if (valid) {
          this.elementFormLoading = true
          try {
            const isEdit = !!this.elementForm.id
            let response

            if (isEdit) {
              response = await funElementApi.update(this.elementForm.id, this.elementForm)
            } else {
              response = await funElementApi.create(this.elementForm)
            }

            if (response.code === 0) {
              this.$Message.success(isEdit ? '更新成功' : '添加成功')
              this.elementFormModal = false
              this.loadElementData()
            } else {
              this.$Message.error(response.message || '操作失败')
            }
          } catch (error) {
            console.error('保存字段失败:', error)
            this.$Message.error('操作失败')
          } finally {
            this.elementFormLoading = false
          }
        }
      })
    },

    // 取消字段表单
    handleElementFormCancel() {
      this.elementFormModal = false
      // 重置表单数据和验证状态
      this.$nextTick(() => {
        if (this.$refs.elementForm) {
          this.$refs.elementForm.resetFields()
        }
      })
    },

    // 工作流部署按钮
    handleDeploy(row) {
      if (!row.bpmnXml) {
        this.$Message.warning('该工作流没有BPMN文件，无法部署')
        return
      }
      this.$Modal.confirm({
        title: '部署确认',
        content: `确定要部署 "${row.workflowName}" 到工作流引擎吗？`,
        onOk: async () => {
          try {
            this.$Message.loading('正在部署工作流...')
            const response = await workflowApi.erpDeployWorkflow(row)

            if (response.code === 0) {
              this.$Message.success('工作流部署成功！')
              console.log('部署结果:', response.data)

              // 可以在这里更新表格数据，标记为已部署
              this.loadTableData()
            } else {
              this.$Message.error(response.message || '工作流部署失败')
            }
          } catch (error) {
            console.error('工作流部署失败:', error)
            this.$Message.error('工作流部署失败: ' + (error.message || '未知错误'))
          }
        }
      })
    },

    // 分页变更
    handlePageChange(page) {
      this.pageInfo.page = Number(page)
      this.loadTableData()
    },

    // 页面大小变更
    handlePageSizeChange(pageSize) {
      this.pageInfo.limit = Number(pageSize)
      this.pageInfo.page = 1
      this.loadTableData()
    },

    // ========== 流程功能管理相关方法 ==========

    // 打开流程功能管理弹窗
    handleFunctionManage() {
      this.functionModalVisible = true
      this.loadFunctionData()
    },

    // 加载功能数据
    async loadFunctionData() {
      this.functionLoading = true

      try {
        const response = await workflowApi.getFunctionTypeList()
        if (response.code === 0) {
          this.functionData = response.data.records || []
        } else {
          this.$Message.error(response.message || '获取功能类型列表失败')
        }
      } catch (error) {
        console.error('获取功能类型列表失败:', error)
        this.$Message.error('获取功能类型列表失败')
      } finally {
        this.functionLoading = false
      }
    },

    // 新增功能
    handleFunctionAdd() {
      this.functionEditMode = 'add'
      this.functionForm = {
        id: null,
        key: '',
        name: ''
      }
      this.functionEditModalVisible = true
      this.$nextTick(() => {
        this.$refs.functionForm.resetFields()
      })
    },

    // 编辑功能
    handleFunctionEdit(row) {
      this.functionEditMode = 'edit'
      this.functionForm = {
        id: row.id,
        key: row.key,
        name: row.name
      }
      this.functionEditModalVisible = true
    },

    // 保存功能
    handleFunctionSave() {
      this.$refs.functionForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (this.functionEditMode === 'add') {
              // 新增功能
              response = await workflowApi.addFunctionType(this.functionForm)
            } else {
              // 编辑功能
              response = await workflowApi.updateFunctionType(this.functionForm)
            }

            if (response.code === 0) {
              this.$Message.success(response.message || (this.functionEditMode === 'add' ? '功能新增成功' : '功能编辑成功'))
              this.functionEditModalVisible = false
              // 重新加载数据
              this.loadFunctionData()
            } else {
              this.$Message.error(response.message || '操作失败')
            }
          } catch (error) {
            console.error('保存功能失败:', error)
            this.$Message.error('操作失败，请重试')
          }
        }
      })
    },

    // 删除功能
    handleFunctionDelete(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除功能"${row.name}"吗？此操作不可恢复。`,
        onOk: async () => {
          try {
            const response = await workflowApi.deleteFunctionType(row.id)
            if (response.code === 0) {
              this.$Message.success(response.message || '功能删除成功')
              // 重新加载数据
              this.loadFunctionData()
            } else {
              this.$Message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除功能失败:', error)
            this.$Message.error('删除失败，请重试')
          }
        }
      })
    }
  }
}
</script>



<style lang="less" scoped>
.workflow-list {
  padding: 16px;

  .search-form {
    margin-bottom: 16px;

    .ivu-form-item {
      margin-bottom: 0;
      margin-right: 16px;
    }
  }

  .action-buttons {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    .right-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .pagination-wrapper {
    margin-top: 16px;
    text-align: right;
  }

  .workflow-view {
    .workflow-info {
      margin-bottom: 16px;

      .info-item {
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        .info-label {
          font-weight: 500;
          color: #515a6e;
          min-width: 80px;
        }

        .info-value {
          color: #17233d;
        }
      }
    }

    .workflow-preview {
      .preview-canvas {
        position: relative;
        width: 100%;
        height: 400px;
        background: #f8f9fa;
        border: 1px solid #e8eaec;
        border-radius: 4px;
        overflow: hidden;

        .preview-node {
          position: absolute;
          width: 50px;
          height: 25px;
          background: white;
          border: 1px solid #dcdee2;
          border-radius: 3px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          user-select: none;

          &.node-start {
            background: #52c41a;
            color: white;
            border-color: #52c41a;
          }

          &.node-end {
            background: #f5222d;
            color: white;
            border-color: #f5222d;
          }

          &.node-approval {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
          }

          &.node-condition {
            background: #fa8c16;
            color: white;
            border-color: #fa8c16;
            transform: rotate(45deg);

            .node-title {
              transform: rotate(-45deg);
            }
          }

          &.node-task {
            background: #f0f0f0;
            color: #333;
          }

          .node-title {
            font-size: 8px;
            font-weight: 500;
            text-align: center;
            line-height: 1;
          }

          i {
            font-size: 10px;
            margin-bottom: 2px;
          }
        }

        .preview-connections {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }
      }
    }
  }

  // 流程功能管理样式
  .function-manage {
    .function-actions {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }
  }
}

// 全局样式覆盖
:deep(.ivu-card-body) {
  padding: 16px;
}

:deep(.ivu-table-wrapper) {
  border: 1px solid #e8eaec;
}

// BPMN XML查看弹窗样式
.bpmn-xml-content {
  .xml-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaec;

    .workflow-name {
      font-size: 16px;
      font-weight: 600;
      color: #2d8cf0;
    }
  }

  .xml-viewer {
    max-height: 500px;
    overflow: auto;
    background: #f8f9fa;
    border: 1px solid #e8eaec;
    border-radius: 4px;
    padding: 16px;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: #333;

      code {
        background: none;
        padding: 0;
        border: none;
        font-size: inherit;
        color: inherit;
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 上新字段管理样式
  .element-manage-content {
    min-height: 400px;
  }

  .element-list {
    padding: 16px 0;
  }

  .add-element-btn {
    margin-bottom: 16px;
  }
}


</style>
