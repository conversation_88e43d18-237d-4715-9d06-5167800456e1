package com.sy.erp.server.service;

import org.springframework.data.domain.Pageable;
import java.util.Map;

/**
 * 工作流服务接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface WorkflowService {

    /**
     * 部署流程定义
     */
    Map<String, Object> deployProcess(String processName, String processKey, String bpmnXml);

    /**
     * 删除部署
     */
    Map<String, Object> deleteDeployment(String deploymentId, boolean cascade);

    /**
     * 启动流程实例
     */
    Map<String, Object> startProcess(String processDefinitionKey, String businessKey, Map<String, Object> variables);

    /**
     * 完成任务
     */
    Map<String, Object> completeTask(String taskId, Map<String, Object> variables);

    /**
     * 获取流程定义列表
     */
    Map<String, Object> getProcessDefinitions(Pageable pageable);

    /**
     * 获取流程实例列表
     */
    Map<String, Object> getProcessInstances(Pageable pageable);

    /**
     * 获取用户任务列表
     */
    Map<String, Object> getUserTasks(String userId, Pageable pageable);

    /**
     * 获取指定用户的任务列表
     */
    Map<String, Object> getAssignedTasks(String userId, Pageable pageable);

    /**
     * 获取候选任务列表
     */
    Map<String, Object> getCandidateTasks(String userId, Pageable pageable);

    /**
     * 获取流程图
     */
    Map<String, Object> getProcessDiagram(String processInstanceId);

    /**
     * 获取流程历史
     */
    Map<String, Object> getProcessHistory(String processInstanceId);

    /**
     * 挂起流程定义
     */
    Map<String, Object> suspendProcessDefinition(String processDefinitionId);

    /**
     * 激活流程定义
     */
    Map<String, Object> activateProcessDefinition(String processDefinitionId);

    /**
     * 获取流程变量
     */
    Map<String, Object> getProcessVariables(String processInstanceId);

    /**
     * 设置流程变量
     */
    Map<String, Object> setProcessVariables(String processInstanceId, Map<String, Object> variables);
}
