package com.aimo.base.server.service.impl;

import com.aimo.base.client.model.base.BaseFunElement;
import com.aimo.base.server.mapper.base.BaseFunElementMapper;
import com.aimo.base.server.service.base.IBaseFunElementService;
import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.model.PageParams;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 基础功能元素服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class BaseFunElementServiceImpl extends BaseServiceImpl<BaseFunElementMapper, BaseFunElement> implements IBaseFunElementService {

    @Resource
    private BaseFunElementMapper baseFunElementMapper;

    @Override
    public IPage<BaseFunElement> listPage(PageParams<BaseFunElement> pageParams) {
        BaseFunElement query = pageParams.mapToObject(BaseFunElement.class);
        QueryWrapper<BaseFunElement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .like(StringUtils.isNotEmpty(query.getName()), BaseFunElement::getName, query.getName())
                .like(StringUtils.isNotEmpty(query.getElement()), BaseFunElement::getElement, query.getElement())
                .eq(query.getFunType() != null, BaseFunElement::getFunType, query.getFunType())
                .eq(query.getType() != null, BaseFunElement::getType, query.getType())
                .eq(BaseFunElement::getStatus, 0); // 只查询正常状态的记录
        queryWrapper.orderByDesc("create_time");
        return baseFunElementMapper.selectPage(pageParams, queryWrapper);
    }

    @Override
    public List<BaseFunElement> getByFunType(Integer funType) {
        if (funType == null) {
            return null;
        }
        QueryWrapper<BaseFunElement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BaseFunElement::getFunType, funType)
                .eq(BaseFunElement::getStatus, 0);
        queryWrapper.orderByDesc("create_time");
        return baseFunElementMapper.selectList(queryWrapper);
    }

    @Override
    public List<BaseFunElement> getByFunTypeAndType(Integer funType, Integer type) {
        if (funType == null || type == null) {
            return null;
        }
        QueryWrapper<BaseFunElement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BaseFunElement::getFunType, funType)
                .eq(BaseFunElement::getType, type)
                .eq(BaseFunElement::getStatus, 0);
        queryWrapper.orderByDesc("create_time");
        return baseFunElementMapper.selectList(queryWrapper);
    }

    @Override
    public BaseFunElement getByElement(String element) {
        if (StringUtils.isEmpty(element)) {
            return null;
        }
        QueryWrapper<BaseFunElement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BaseFunElement::getElement, element)
                .eq(BaseFunElement::getStatus, 0);
        return baseFunElementMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean checkElement(String element) {
        if (StringUtils.isEmpty(element)) {
            return false;
        }
        QueryWrapper<BaseFunElement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BaseFunElement::getElement, element)
                .eq(BaseFunElement::getStatus, 0);
        return baseFunElementMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean saveBaseFunElement(BaseFunElement baseFunElement) {
        if (baseFunElement == null) {
            return false;
        }
        baseFunElement.setCreateTime(new Date());
        baseFunElement.setCreateUser(OpenHelper.getUserId());
        baseFunElement.setUpdateTime(baseFunElement.getCreateTime());
        baseFunElement.setUpdateUser(baseFunElement.getCreateUser());
        baseFunElement.setStatus(0); // 正常状态
        return baseFunElementMapper.insert(baseFunElement) > 0;
    }

    @Override
    public boolean updateBaseFunElement(BaseFunElement baseFunElement) {
        if (baseFunElement == null || baseFunElement.getId() == null) {
            return false;
        }
        baseFunElement.setUpdateTime(new Date());
        baseFunElement.setUpdateUser(OpenHelper.getUserId());
        return baseFunElementMapper.updateById(baseFunElement) > 0;
    }

    @Override
    public boolean deleteBaseFunElement(Long id) {
        if (id == null) {
            return false;
        }
        BaseFunElement baseFunElement = new BaseFunElement();
        baseFunElement.setId(id);
        baseFunElement.setStatus(1); // 删除状态
        baseFunElement.setUpdateTime(new Date());
        baseFunElement.setUpdateUser(OpenHelper.getUserId());
        return baseFunElementMapper.updateById(baseFunElement) > 0;
    }

    @Override
    public List<BaseFunElement> getAllNormalElements() {
        QueryWrapper<BaseFunElement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BaseFunElement::getStatus, 0);
        queryWrapper.orderByDesc("create_time");
        return baseFunElementMapper.selectList(queryWrapper);
    }
}
