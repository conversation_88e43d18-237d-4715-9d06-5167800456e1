package com.aimo.base.client.model.base;

import com.aimo.common.mybatis.base.entity.UpdateEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 工作流实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("base_workflow")
public class Workflow extends UpdateEntity<Workflow> {
    
    /**
     * 工作流Key
     */
    private String workflowKey;
    
    /**
     * 工作流名称
     */
    private String workflowName;
    
    /**
     * bpmn文件内容
     */
    private String bpmnXml;
    
    /**
     * 停用状态 0:停用 1:启用
     */
    private Integer execute;
    
    /**
     * 状态 0:正常 1:删除
     */
    @TableField(value = "`status`")
    private Integer status;
    
    /**
     * 部署状态 0:未部署 1:已部署
     */
    private Integer deployStatus;
    
    public Workflow(String workflowKey, String workflowName, String bpmnXml, Integer execute, Integer status, Integer deployStatus) {
        super(true);
        this.workflowKey = workflowKey;
        this.workflowName = workflowName;
        this.bpmnXml = bpmnXml;
        this.execute = execute;
        this.status = status;
        this.deployStatus = deployStatus;
    }
}
