package com.sy.ys.client.service;

import com.aimo.common.model.ResultBody;
import com.sy.ys.client.model.othOrder.YsOthOutOrder;
import com.sy.ys.client.param.othOrder.YsOthOutOrderParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IYsOthOutOrderClient {
    @ApiOperation(value = "新增其他出库单", notes = "新增其他出库单")
    @PostMapping("/othOutOrder/addOthOutOrder")
    ResultBody<Long> addOthOutOrder(@RequestBody YsOthOutOrderParam ysOthOutOrderParam);

    @ApiOperation(value = "删除其他出库单", notes = "删除其他出库单")
    @PostMapping("/othOutOrder/delOthOutOrder")
    ResultBody<Boolean> delOthOutOrder(@RequestParam(value = "id") Long id);

    @ApiOperation(value = "获取其他出库单", notes = "获取其他出库单")
    @PostMapping("/othOutOrder/getOthOutOrder")
    ResultBody<YsOthOutOrder> getOthOutOrder(@RequestParam(value = "id") Long id);
}
