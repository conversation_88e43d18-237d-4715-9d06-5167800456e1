<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="03c17b78-41a7-4d9b-a7cb-53de1cb885a6" name="Changes" comment="fix 报关申报页面toFixed报错">
      <change afterPath="$PROJECT_DIR$/src/view/module/base/workflow/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/routers.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/routers.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/view/module/newApply/newApply/newApply/edit.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/view/module/newApply/newApply/newApply/edit.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/view/module/newApply/newApply/newApply/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/view/module/newApply/newApply/newApply/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/node_modules/view-design/src/components/modal/modal.vue" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/node_modules/view-design/src/components/spin/spin.vue" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/node_modules/view-design/src/components/table/table.vue" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zzoSIzHE4T3YOGOK87ORztlGhz" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/view/module/base/workflow&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\Program Files\\JetBrains\\WebStorm 2025.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Users\admini\Desktop\dev\sy_declare_ui\src\view\module\base\workflow" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="03c17b78-41a7-4d9b-a7cb-53de1cb885a6" name="Changes" comment="" />
      <created>1752743260862</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752743260862</updated>
      <workItem from="1752743262248" duration="3634000" />
      <workItem from="1752800408929" duration="2697000" />
      <workItem from="1752819801023" duration="13000" />
      <workItem from="1752819821009" duration="4849000" />
      <workItem from="1752827855436" duration="34000" />
      <workItem from="1752827909777" duration="33000" />
      <workItem from="1752828340272" duration="15000" />
      <workItem from="1752828538897" duration="124000" />
      <workItem from="1753059835404" duration="3608000" />
      <workItem from="1753067862837" duration="14054000" />
      <workItem from="1753145004973" duration="12006000" />
      <workItem from="1753231220143" duration="10494000" />
      <workItem from="1753318985107" duration="19613000" />
      <workItem from="1753407924254" duration="8276000" />
      <workItem from="1753664430099" duration="14259000" />
      <workItem from="1753854979803" duration="3090000" />
      <workItem from="1753860902282" duration="1409000" />
    </task>
    <task id="LOCAL-00001" summary="fix 报关申报页面toFixed报错">
      <option name="closed" value="true" />
      <created>1753146470898</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753146470898</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix 报关申报页面toFixed报错" />
    <option name="LAST_COMMIT_MESSAGE" value="fix 报关申报页面toFixed报错" />
  </component>
</project>