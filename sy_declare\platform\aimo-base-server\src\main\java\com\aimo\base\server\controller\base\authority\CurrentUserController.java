package com.aimo.base.server.controller.base.authority;

import com.aimo.base.client.model.base.BaseUser;
import com.aimo.base.client.vo.AuthorityMenu;
import com.aimo.base.server.service.BaseAuthorityService;
import com.aimo.base.server.service.base.BaseUserService;
import com.aimo.common.model.ResultBody;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.security.OpenUserDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: ChiYe
 * @date: 2019/5/24 13:31
 * @description:
 */
@Api(value = "当前登陆用户", tags = "当前登陆用户")
@RestController
public class CurrentUserController {

    @Resource
    private BaseUserService baseUserService;
    @Resource
    private BaseAuthorityService baseAuthorityService;

    /**
     * 修改当前登录用户密码
     */
    @ApiOperation(value = "修改当前登录用户密码", notes = "修改当前登录用户密码")
    @GetMapping("/current/user/rest/password")
    public ResultBody<Boolean> restPassword(@RequestParam(value = "password") String password) {
        baseUserService.updatePassword(OpenHelper.getUserId(), password);
        return ResultBody.ok(true);
    }

    /**
     * 获取当前登录用户信息
     */
    @ApiOperation(value = "获取当前登录用户信息", notes = "获取当前登录用户信息")
    @GetMapping("/current/user/info")
    public BaseUser getCurrentUser() {
        try {
            // 直接从OpenHelper获取用户信息
            OpenUserDetails userDetails = OpenHelper.getUser();
            if (userDetails != null) {
                // 从OpenHelper中获取用户信息，构造BaseUser对象
                BaseUser user = new BaseUser();
                user.setId(userDetails.getUserId());
                user.setUserName(userDetails.getUsername());
                user.setNickName(userDetails.getNickName());
                return user;
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户ID
     */
    @ApiOperation(value = "获取当前登录用户ID", notes = "获取当前登录用户ID")
    @GetMapping("/current/user/id")
    public ResultBody<String> getCurrentUserId() {
        try {
            OpenUserDetails userDetails = OpenHelper.getUser();
            if (userDetails != null && userDetails.getUserId() != null) {
                String userId = String.valueOf(userDetails.getUserId());
                return ResultBody.ok(userId);
            } else {
                return ResultBody.failed().msg("用户未登录");
            }
        } catch (Exception e) {
            return ResultBody.failed().msg("获取用户ID失败");
        }
    }

    /**
     * 获取登陆用户已分配权限
     */
    @ApiOperation(value = "获取当前登录用户已分配菜单权限", notes = "获取当前登录用户已分配菜单权限")
    @GetMapping("/current/user/menu")
    public ResultBody<List<AuthorityMenu>> findAuthorityMenu() {
        OpenUserDetails userDetails = OpenHelper.getUser();
        return ResultBody.ok(baseAuthorityService.findAuthorityMenuByUser(OpenHelper.getUserId(), userDetails != null && userDetails.getSuperAuth()));
    }


}
