package com.aimo.base.server.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 功能字段元素DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "BaseFunElementDTO对象", description = "功能字段元素DTO")
public class BaseFunElementDTO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称", required = true)
    @NotBlank(message = "字段名称不能为空")
    private String name;

    /**
     * 字段英文
     */
    @ApiModelProperty(value = "字段英文", required = true)
    @NotBlank(message = "字段英文不能为空")
    private String element;

    /**
     * 功能枚举值：上新功能10001
     */
    @ApiModelProperty(value = "功能枚举值：上新功能10001", required = true)
    @NotNull(message = "功能类型不能为空")
    private Integer funType;

    /**
     * 类型 1：基础信息 2：标准信息
     */
    @ApiModelProperty(value = "类型 1：基础信息 2：标准信息", required = true)
    @NotNull(message = "字段类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "删除", required = true)
    @NotNull(message = "类型不能为空")
    private Integer status;
}
