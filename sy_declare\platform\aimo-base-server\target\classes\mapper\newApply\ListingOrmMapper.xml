<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aimo.base.server.mapper.newApply.ListingOrmMapper">
    <select id="listListingOrm" resultType="com.aimo.base.client.model.newApply.ListingOrm">
        select o.*,a.name as shop_name,d.nick_name as seller_name,
               e.asin,e.parent_asin,e.fnsku,f.spec as product_spec,f.name as product_name,f.color as product_color,f.size as product_size,f.inseam as product_inseam
        from bussiness_listing_orm o left join base_shop a on o.shop_id = a.id
         left join base_user d on o.seller_id = d.id
         left join LX_LISTING e on o.seller_sku = e.seller_sku and a.sid = e.sid
         left join base_product f on o.product_id = f.id
        where o.shop_id = #{shopId} and o.listing_title = #{listingTitle}
    </select>
    <select id="listListingOrmByKeyList" resultType="com.aimo.base.client.model.newApply.ListingOrm">
        select o.*
        from bussiness_listing_orm o
        <where>
        <if test="ormKeyList != null and ormKeyList.size>0">
            <foreach separator="," item="ormKey" open=" and concat(o.shop_id,',',o.seller_sku,',',IFNULL(o.fnsku,''))  in(" close=")" collection="ormKeyList">
                #{ormKey}
            </foreach>
        </if>
        </where>
    </select>
    <select id="selectPages" resultType="com.aimo.base.client.model.newApply.ListingOrm" parameterType="com.aimo.base.client.param.newApply.ListingOrmParam">
        select o.*,a.name as shop_name,b.nick_name as create_user_name,c.nick_name as update_user_name,d.nick_name as seller_name,
               f.code as product_code,f.spec as product_spec,f.name as product_name,
               f.color as product_color,f.size as product_size, f.inseam as product_inseam
        from bussiness_listing_orm o left join base_shop a on o.shop_id = a.id
        left join base_user b on o.create_user = b.id
        left join base_user c on o.update_user = c.id
        left join base_user d on o.seller_id = d.id
        left join base_product f on o.product_id = f.id
        <where>
            <if test="param.idList != null and param.idList.size>=1">
                <foreach separator="," item="item" open=" and o.id in(" close=")" collection="param.idList">
                    #{item}
                </foreach>
            </if>
            <if test="param.shopIdList.size>=1">
                <foreach separator="," item="item" open=" and o.shop_id in(" close=")" collection="param.shopIdList">
                    #{item}
                </foreach>
            </if>
            <if test="param.sellerIdList.size>=1">
                <foreach separator="," item="item" open=" and o.seller_id in(" close=")" collection="param.sellerIdList">
                    #{item}
                </foreach>
            </if>
            <if test="param.sellerSkuList.size>1">
                <foreach separator="," item="item" open=" and o.seller_sku in(" close=")" collection="param.sellerSkuList">
                    #{item}
                </foreach>
            </if>
            <if test="param.sellerSkuList.size==1">
                and o.seller_sku LIKE CONCAT('%',#{param.sellerSkus},'%')
            </if>
            <if test="param.productCodeList.size>1">
                <foreach separator="," item="item" open=" and o.product_code in(" close=")" collection="param.productCodeList">
                    #{item}
                </foreach>
            </if>
            <if test="param.productCodeList.size==1">
                and o.product_code LIKE CONCAT('%',#{param.productCodes},'%')
            </if>
            <if test="param.listingTitle !=null and param.listingTitle !=''">
                and o.listing_title LIKE CONCAT('%',#{param.listingTitle},'%')
            </if>
            <if test="param.status !=null">
                and o.status = #{param.status}
            </if>
        </where>
        order by o.id DESC
    </select>
    <select id="getPredictionAsinId" resultType="java.lang.Long">
        select prediction_asin_id from bussiness_listing_orm o join base_shop a on o.shop_id = a.id left join base_shop b on a.name_key = b.name_key
        where b.id = #{shopId} and o.seller_sku = #{sellerSku} and prediction_asin_id is not null limit 1;
    </select>
</mapper>
