{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue", "mtime": 1753933128519}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["workflowApi", "funElementApi", "name", "data", "_this", "searchForm", "workflowName", "execute", "columns", "title", "type", "width", "align", "key", "min<PERSON><PERSON><PERSON>", "render", "h", "params", "row", "bpmnXml", "props", "size", "on", "click", "handleViewBpmn", "slot", "tableData", "loading", "pageInfo", "page", "limit", "total", "currentWorkflow", "bpmnViewModalVisible", "currentBpmnWorkflow", "currentBpmnXml", "elementManageModal", "activeElementType", "basicElementList", "standardElementList", "elementFormModal", "elementFormLoading", "elementForm", "id", "element", "funType", "elementColumns", "style", "background", "padding", "borderRadius", "fontSize", "fontFamily", "createTime", "marginRight", "handleEditElement", "handleDeleteElement", "elementFormRules", "required", "message", "trigger", "pattern", "functionModalVisible", "functionEditModalVisible", "functionEditMode", "functionLoading", "functionData", "functionForm", "functionRules", "functionColumns", "computed", "elementFormTitle", "mounted", "loadTableData", "methods", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "response", "wrap", "_callee$", "_context", "prev", "next", "getWorkflowPage", "sent", "code", "records", "Number", "$Message", "error", "t0", "console", "finish", "stop", "handleSearch", "log", "handleReset", "handleRefresh", "handleAdd", "$router", "push", "warning", "copyBpmnXml", "_this3", "navigator", "clipboard", "writeText", "then", "success", "catch", "textArea", "document", "createElement", "value", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "err", "<PERSON><PERSON><PERSON><PERSON>", "handleEdit", "concat", "handleDelete", "_this4", "$Modal", "confirm", "content", "onOk", "_onOk", "_callee2", "_callee2$", "_context2", "deleteWorkflow", "apply", "arguments", "handleToggleStatus", "_this5", "_callee3", "newStatus", "statusText", "updateData", "_callee3$", "_context3", "updateWorkflow", "handleElementManage", "loadElementData", "_this6", "_callee4", "allElements", "_callee4$", "_context4", "getList", "filter", "item", "handleElementTypeChange", "handleAddElement", "_this7", "parseInt", "$nextTick", "$refs", "clearValidate", "_this8", "_this9", "_onOk2", "_callee5", "_callee5$", "_context5", "delete", "handleElementModalCancel", "handleElementFormSubmit", "_this10", "_callee7", "_callee7$", "_context7", "validate", "_ref", "_callee6", "valid", "isEdit", "_callee6$", "_context6", "update", "create", "_x", "handleElementFormCancel", "_this11", "resetFields", "handleDeploy", "_this12", "_onOk3", "_callee8", "_callee8$", "_context8", "erpDeployWorkflow", "handlePageChange", "handlePageSizeChange", "pageSize", "handleFunctionManage", "loadFunctionData", "_this13", "_callee9", "_callee9$", "_context9", "getFunctionTypeList", "handleFunctionAdd", "_this14", "handleFunctionEdit", "handleFunctionSave", "_this15", "_ref2", "_callee10", "_callee10$", "_context10", "addFunctionType", "updateFunctionType", "_x2", "handleFunctionDelete", "_this16", "_onOk4", "_callee11", "_callee11$", "_context11", "deleteFunctionType"], "sources": ["src/view/module/base/workflow/index.vue"], "sourcesContent": ["<template>\n  <div class=\"workflow-list\">\n    <Card>\n      <!-- 搜索区域 -->\n      <Form ref=\"searchForm\" :model=\"searchForm\" inline class=\"search-form\">\n        <FormItem>\n          <Input\n            v-model=\"searchForm.workflowName\"\n            placeholder=\"请输入工作流名称\"\n            style=\"width: 200px\"\n            @on-enter=\"handleSearch\"\n          />\n        </FormItem>\n        <FormItem>\n          <Select\n            v-model=\"searchForm.execute\"\n            placeholder=\"请选择状态\"\n            style=\"width: 120px\"\n            clearable\n          >\n            <Option :value=\"1\">启用</Option>\n            <Option :value=\"0\">停用</Option>\n          </Select>\n        </FormItem>\n        <FormItem>\n          <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\n          <Button @click=\"handleReset\" style=\"margin-left: 8px\">重置</Button>\n        </FormItem>\n      </Form>\n\n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <Button type=\"primary\" icon=\"md-add\" @click=\"handleAdd\">新增流程</Button>\n        <div class=\"right-buttons\">\n          <Button type=\"warning\" icon=\"md-rocket\" @click=\"handleElementManage\" style=\"margin-right: 8px\">上新字段管理</Button>\n          <Button type=\"success\" icon=\"md-settings\" @click=\"handleFunctionManage\">流程功能</Button>\n        </div>\n      </div>\n\n      <!-- 数据表格 -->\n      <Table\n        :columns=\"columns\"\n        :data=\"tableData\"\n        :loading=\"loading\"\n        stripe\n        :max-height=\"600\"\n      >\n        <template v-slot:status=\"{ row }\">\n          <Badge v-if=\"row.execute === 1\" status=\"success\" text=\"启用\" />\n          <Badge v-else status=\"error\" text=\"停用\" />\n        </template>\n\n        <template v-slot:deployStatus=\"{ row }\">\n          <Badge v-if=\"row.deployStatus === 1\" status=\"success\" text=\"已部署\" />\n          <Badge v-else status=\"error\" text=\"未部署\" />\n        </template>\n\n        <template v-slot:nodeCount=\"{ row }\">\n          <Tag color=\"blue\">{{ row.nodeCount || 0 }}个节点</Tag>\n        </template>\n\n        <template v-slot:action=\"{ row }\">\n          <Button v-if=\"row.deployStatus === 0\" type=\"error\" size=\"small\" @click=\"handleDeploy(row)\" style=\"margin-right: 4px;\">部署</Button>\n          <Button type=\"primary\" size=\"small\" @click=\"handleEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\n          <Button\n            :type=\"row.execute === 1 ? 'warning' : 'success'\"\n            size=\"small\"\n            @click=\"handleToggleStatus(row)\"\n            style=\"margin-right: 4px;\"\n          >\n            {{ row.execute === 1 ? '停用' : '启用' }}\n          </Button>\n          <Button type=\"error\" size=\"small\" @click=\"handleDelete(row)\">删除</Button>\n        </template>\n      </Table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <Page\n          :total=\"pageInfo.total\"\n          :current=\"pageInfo.page\"\n          :page-size=\"pageInfo.limit\"\n          show-elevator\n          show-sizer\n          show-total\n          @on-change=\"handlePageChange\"\n          @on-page-size-change=\"handlePageSizeChange\"\n        />\n      </div>\n    </Card>\n\n    <!-- 上新字段管理弹窗 -->\n    <Modal\n      v-model=\"elementManageModal\"\n      title=\"上新字段管理\"\n      width=\"800\"\n      :mask-closable=\"false\"\n      @on-cancel=\"handleElementModalCancel\"\n    >\n      <div class=\"element-manage-content\">\n        <!-- 分类标签 -->\n        <Tabs v-model=\"activeElementType\" @on-click=\"handleElementTypeChange\">\n          <TabPane label=\"基础信息字段\" name=\"1\">\n            <div class=\"element-list\">\n              <!-- 添加按钮 -->\n              <div class=\"add-element-btn\">\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\n                  添加基础信息字段\n                </Button>\n              </div>\n              <!-- 字段列表 -->\n              <Table\n                :columns=\"elementColumns\"\n                :data=\"basicElementList\"\n                :loading=\"false\"\n                size=\"small\"\n                stripe\n              ></Table>\n            </div>\n          </TabPane>\n          <TabPane label=\"标准信息字段\" name=\"2\">\n            <div class=\"element-list\">\n              <!-- 添加按钮 -->\n              <div class=\"add-element-btn\">\n                <Button type=\"dashed\" icon=\"md-add\" @click=\"handleAddElement\" long>\n                  添加标准信息字段\n                </Button>\n              </div>\n              <!-- 字段列表 -->\n              <Table\n                :columns=\"elementColumns\"\n                :data=\"standardElementList\"\n                :loading=\"false\"\n                size=\"small\"\n                stripe\n              ></Table>\n            </div>\n          </TabPane>\n        </Tabs>\n      </div>\n      <div slot=\"footer\">\n        <Button @click=\"handleElementModalCancel\">关闭</Button>\n      </div>\n    </Modal>\n\n    <!-- 添加/编辑字段弹窗 -->\n    <Modal\n      v-model=\"elementFormModal\"\n      :title=\"elementFormTitle\"\n      width=\"500\"\n      :mask-closable=\"false\"\n      @on-cancel=\"handleElementFormCancel\"\n    >\n      <Form ref=\"elementForm\" :model=\"elementForm\" :rules=\"elementFormRules\" :label-width=\"100\">\n        <FormItem label=\"字段名称\" prop=\"name\">\n          <Input v-model=\"elementForm.name\" placeholder=\"请输入字段名称\" />\n        </FormItem>\n        <FormItem label=\"字段英文\" prop=\"element\">\n          <Input v-model=\"elementForm.element\" placeholder=\"请输入字段英文标识\" />\n        </FormItem>\n        <FormItem label=\"字段类型\" prop=\"type\">\n          <RadioGroup v-model=\"elementForm.type\">\n            <Radio :label=\"1\">基础信息</Radio>\n            <Radio :label=\"2\">标准信息</Radio>\n          </RadioGroup>\n        </FormItem>\n      </Form>\n      <div slot=\"footer\">\n        <Button @click=\"handleElementFormCancel\">取消</Button>\n        <Button type=\"primary\" :loading=\"elementFormLoading\" @click=\"handleElementFormSubmit\">\n          {{ elementForm.id ? '更新' : '添加' }}\n        </Button>\n      </div>\n    </Modal>\n\n    <!-- 流程功能管理弹窗 -->\n    <Modal v-model=\"functionModalVisible\" title=\"流程功能管理\" width=\"80%\" :mask-closable=\"false\">\n      <div class=\"function-manage\">\n        <!-- 功能操作按钮 -->\n        <div class=\"function-actions\">\n          <Button type=\"primary\" icon=\"md-add\" @click=\"handleFunctionAdd\" style=\"margin-right: 4px;\">新增功能</Button>\n          <Button icon=\"md-refresh\" @click=\"loadFunctionData\">刷新</Button>\n        </div>\n\n        <!-- 功能列表表格 -->\n        <Table\n          :columns=\"functionColumns\"\n          :data=\"functionData\"\n          :loading=\"functionLoading\"\n          stripe\n          :max-height=\"400\"\n          style=\"margin-top: 16px;\"\n        >\n          <template v-slot:status=\"{ row }\">\n            <Badge v-if=\"row.status === 0\" status=\"success\" text=\"正常\" />\n            <Badge v-else status=\"error\" text=\"删除\" />\n          </template>\n\n          <template v-slot:action=\"{ row }\">\n            <Button type=\"primary\" size=\"small\" @click=\"handleFunctionEdit(row)\" style=\"margin-right: 4px;\">编辑</Button>\n            <Button type=\"error\" size=\"small\" @click=\"handleFunctionDelete(row)\">删除</Button>\n          </template>\n        </Table>\n      </div>\n\n      <div slot=\"footer\">\n        <Button @click=\"functionModalVisible = false\">关闭</Button>\n      </div>\n    </Modal>\n\n    <!-- 功能编辑弹窗 -->\n    <Modal v-model=\"functionEditModalVisible\" :title=\"functionEditMode === 'add' ? '新增功能' : '编辑功能'\" @on-ok=\"handleFunctionSave\">\n      <Form :model=\"functionForm\" :rules=\"functionRules\" ref=\"functionForm\" :label-width=\"80\">\n        <FormItem label=\"功能KEY\" prop=\"key\">\n          <Input v-model=\"functionForm.key\" placeholder=\"请输入功能KEY\" :disabled=\"functionEditMode === 'edit'\" />\n        </FormItem>\n        <FormItem label=\"功能名称\" prop=\"name\">\n          <Input v-model=\"functionForm.name\" placeholder=\"请输入功能名称\" />\n        </FormItem>\n      </Form>\n    </Modal>\n\n    <!-- BPMN XML查看弹窗 -->\n    <Modal v-model=\"bpmnViewModalVisible\" title=\"BPMN XML内容\" width=\"80%\" :mask-closable=\"false\">\n      <div class=\"bpmn-xml-content\">\n        <div class=\"xml-header\">\n          <span class=\"workflow-name\">{{ currentBpmnWorkflow.workflowName }}</span>\n          <Button type=\"primary\" size=\"small\" @click=\"copyBpmnXml\" style=\"float: right;\">\n            <Icon type=\"md-copy\" />\n            复制XML\n          </Button>\n        </div>\n        <div class=\"xml-viewer\">\n          <pre><code>{{ currentBpmnXml }}</code></pre>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <Button @click=\"bpmnViewModalVisible = false\">关闭</Button>\n      </div>\n    </Modal>\n  </div>\n</template>\n\n<script>\nimport workflowApi from '@/api/base/workflow'\nimport funElementApi from '@/api/base/funElement'\n\nexport default {\n  name: 'WorkflowList',\n  data() {\n    return {\n      // 搜索表单\n      searchForm: {\n        workflowName: '',\n        execute: null\n      },\n\n      // 表格列定义\n      columns: [\n        {\n          title: '序号',\n          type: 'index',\n          width: 50,\n          align: 'center'\n        },\n        {\n          title: '工作流Key',\n          key: 'workflowKey',\n          width: 150,\n          align: 'center'\n        },\n        {\n          title: '工作流名称',\n          key: 'workflowName',\n          minWidth: 150\n        },\n        {\n          title: 'BPMN文件',\n          key: 'bpmnXml',\n          width: 120,\n          align: 'center',\n          render: (h, params) => {\n            if (params.row.bpmnXml) {\n              return h('Button', {\n                props: {\n                  type: 'text',\n                  size: 'small'\n                },\n                on: {\n                  click: () => {\n                    this.handleViewBpmn(params.row)\n                  }\n                }\n              }, '查看文件')\n            } else {\n              return h('span', '无文件')\n            }\n          }\n        },\n        {\n          title: '状态',\n          key: 'execute',\n          width: 80,\n          align: 'center',\n          slot: 'status'\n        },\n        {\n          title: '部署状态',\n          key: 'deployStatus',\n          width: 150,\n          align: 'center',\n          slot: 'deployStatus'\n        },\n        {\n          title: '创建人',\n          key: 'createUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '更新人',\n          key: 'updateUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '创建时间',\n          key: 'createTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '更新时间',\n          key: 'updateTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 280,\n          align: 'center',\n          slot: 'action'\n        }\n      ],\n\n      // 表格数据\n      tableData: [],\n\n      // 加载状态\n      loading: false,\n\n      // 分页信息\n      pageInfo: {\n        page: 1,\n        limit: 10,\n        total: 0\n      },\n      currentWorkflow: {},\n\n      // BPMN查看相关\n      bpmnViewModalVisible: false,\n      currentBpmnWorkflow: {},\n      currentBpmnXml: '',\n\n      // 上新字段管理相关数据\n      elementManageModal: false,\n      activeElementType: '1', // 1: 基础信息, 2: 标准信息\n      basicElementList: [],\n      standardElementList: [],\n      elementFormModal: false,\n      elementFormLoading: false,\n      elementForm: {\n        id: null,\n        name: '',\n        element: '',\n        type: 1,\n        funType: 10001 // 上新功能类型枚举\n      },\n      // 表格列定义\n      elementColumns: [\n        {\n          title: '序号',\n          type: 'index',\n          width: 60,\n          align: 'center'\n        },\n        {\n          title: '字段名称',\n          key: 'name',\n          minWidth: 120\n        },\n        {\n          title: '字段英文',\n          key: 'element',\n          minWidth: 150,\n          render: (h, params) => {\n            return h('code', {\n              style: {\n                background: '#f5f5f5',\n                padding: '2px 6px',\n                borderRadius: '3px',\n                fontSize: '12px',\n                fontFamily: 'Courier New, monospace'\n              }\n            }, params.row.element)\n          }\n        },\n        {\n          title: '创建时间',\n          key: 'createTime',\n          width: 150,\n          render: (h, params) => {\n            return h('span', params.row.createTime ? params.row.createTime : '-')\n          }\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 200,\n          align: 'center',\n          render: (h, params) => {\n            return h('div', [\n              h('Button', {\n                props: {\n                  type: 'primary',\n                  size: 'small'\n                },\n                style: {\n                  marginRight: '5px'\n                },\n                on: {\n                  click: () => {\n                    this.handleEditElement(params.row)\n                  }\n                }\n              }, [\n                '编辑'\n              ]),\n              h('Button', {\n                props: {\n                  type: 'error',\n                  size: 'small'\n                },\n                on: {\n                  click: () => {\n                    this.handleDeleteElement(params.row)\n                  }\n                }\n              }, [\n                '删除'\n              ])\n            ])\n          }\n        }\n      ],\n      elementFormRules: {\n        name: [\n          { required: true, message: '请输入字段名称', trigger: 'blur' }\n        ],\n        element: [\n          { required: true, message: '请输入字段英文标识', trigger: 'blur' },\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段英文必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, type: 'number', message: '请选择字段类型', trigger: 'change' }\n        ]\n      },\n\n      // 流程功能管理相关\n      functionModalVisible: false,\n      functionEditModalVisible: false,\n      functionEditMode: 'add', // add | edit\n      functionLoading: false,\n      functionData: [],\n      functionForm: {\n        id: null,\n        key: '',\n        name: ''\n      },\n      functionRules: {\n        key: [\n          { required: true, message: '请输入功能KEY', trigger: 'blur' },\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '功能KEY必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        name: [\n          { required: true, message: '请输入功能名称', trigger: 'blur' }\n        ]\n      },\n      functionColumns: [\n        {\n          title: '序号',\n          type: 'index',\n          width: 60,\n          align: 'center'\n        },\n        {\n          title: '功能KEY',\n          key: 'key',\n          minWidth: 150\n        },\n        {\n          title: '功能名称',\n          key: 'name',\n          minWidth: 150\n        },\n        {\n          title: '创建人',\n          key: 'createUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '更新人',\n          key: 'updateUserName',\n          width: 140,\n          align: 'center'\n        },\n        {\n          title: '创建时间',\n          key: 'createTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '更新时间',\n          key: 'updateTime',\n          width: 160,\n          align: 'center'\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 150,\n          align: 'center',\n          slot: 'action'\n        }\n      ]\n    }\n  },\n\n  computed: {\n    // 表单标题\n    elementFormTitle() {\n      return this.elementForm.id ? '编辑字段' : '添加字段'\n    }\n  },\n\n  mounted() {\n    this.loadTableData()\n  },\n\n  methods: {\n    // 加载表格数据\n    async loadTableData() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pageInfo.page,\n          size: this.pageInfo.limit,\n          workflowName: this.searchForm.workflowName,\n          execute: this.searchForm.execute\n        }\n\n        const response = await workflowApi.getWorkflowPage(params)\n        if (response.code === 0) {\n          this.tableData = response.data.records || []\n          this.pageInfo.total = Number(response.data.total) || 0\n        } else {\n          this.$Message.error(response.message || '获取工作流列表失败')\n        }\n      } catch (error) {\n        console.error('获取工作流列表失败:', error)\n        this.$Message.error('获取工作流列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 搜索\n    handleSearch() {\n      console.log('搜索条件:', this.searchForm)\n      this.pageInfo.page = 1\n      this.loadTableData()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        workflowName: '',\n        execute: null\n      }\n      this.handleSearch()\n    },\n\n    // 刷新\n    handleRefresh() {\n      this.loadTableData()\n    },\n\n    // 新增流程\n    handleAdd() {\n      this.$router.push('/base/workflow/add')\n    },\n\n    // 查看BPMN文件\n    handleViewBpmn(row) {\n      if (row.bpmnXml) {\n        this.currentBpmnWorkflow = row\n        this.currentBpmnXml = row.bpmnXml\n        this.bpmnViewModalVisible = true\n      } else {\n        this.$Message.warning('该工作流暂无BPMN文件')\n      }\n    },\n\n    // 复制BPMN XML内容\n    copyBpmnXml() {\n      if (navigator.clipboard && navigator.clipboard.writeText) {\n        navigator.clipboard.writeText(this.currentBpmnXml).then(() => {\n          this.$Message.success('BPMN XML已复制到剪贴板')\n        }).catch(() => {\n          this.$Message.error('复制失败，请手动复制')\n        })\n      } else {\n        // 兼容旧浏览器\n        const textArea = document.createElement('textarea')\n        textArea.value = this.currentBpmnXml\n        document.body.appendChild(textArea)\n        textArea.select()\n        try {\n          document.execCommand('copy')\n          this.$Message.success('BPMN XML已复制到剪贴板')\n        } catch (err) {\n          this.$Message.error('复制失败，请手动复制')\n        }\n        document.body.removeChild(textArea)\n      }\n    },\n\n    // 编辑工作流定义\n    handleEdit(row) {\n      this.$router.push(`/base/workflow/add/${row.id}`)\n    },\n\n    // 删除工作流定义\n    handleDelete(row) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        content: `确定要删除\"${row.workflowName}\"工作流吗？删除后不可恢复。`,\n        onOk: async () => {\n          try {\n            const response = await workflowApi.deleteWorkflow(row.id)\n            if (response.code === 0) {\n              this.$Message.success(response.message || '工作流删除成功')\n              // 重新加载数据\n              this.loadTableData()\n            } else {\n              this.$Message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除工作流失败:', error)\n            this.$Message.error('删除失败，请重试')\n          }\n        }\n      })\n    },\n\n    // 切换启用/停用状态\n    async handleToggleStatus(row) {\n      try {\n        const newStatus = row.execute === 1 ? 0 : 1\n        const statusText = newStatus === 1 ? '启用' : '停用'\n\n        const updateData = {\n          id: row.id,\n          execute: newStatus\n        }\n\n        const response = await workflowApi.updateWorkflow(updateData)\n        if (response.code === 0) {\n          this.$Message.success(`${statusText}成功`)\n          // 更新本地数据\n          row.execute = newStatus\n        } else {\n          this.$Message.error(response.message || `${statusText}失败`)\n        }\n      } catch (error) {\n        console.error('切换状态失败:', error)\n        this.$Message.error('操作失败，请重试')\n      }\n    },\n\n    // ========== 上新字段管理相关方法 ==========\n\n    // 上新字段管理\n    handleElementManage() {\n      this.elementManageModal = true\n      this.activeElementType = '1'\n      this.loadElementData()\n    },\n\n    // 加载字段数据\n    async loadElementData() {\n      try {\n        const response = await funElementApi.getList({\n          funType: 10001 // 上新功能类型\n        })\n\n        if (response.code === 0) {\n          const allElements = response.data || []\n          // 按类型分组\n          this.basicElementList = allElements.filter(item => item.type === 1)\n          this.standardElementList = allElements.filter(item => item.type === 2)\n        } else {\n          this.$Message.error(response.message || '加载字段数据失败')\n        }\n      } catch (error) {\n        console.error('加载字段数据失败:', error)\n        this.$Message.error('加载字段数据失败')\n      }\n    },\n\n    // 切换字段类型\n    handleElementTypeChange(name) {\n      this.activeElementType = name\n    },\n\n    // 添加字段\n    handleAddElement() {\n      this.elementForm = {\n        id: null,\n        name: '',\n        element: '',\n        type: parseInt(this.activeElementType),\n        funType: 10001\n      }\n      this.elementFormModal = true\n\n      // 清除表单验证状态\n      this.$nextTick(() => {\n        if (this.$refs.elementForm) {\n          this.$refs.elementForm.clearValidate()\n        }\n      })\n    },\n\n    // 编辑字段\n    handleEditElement(item) {\n      this.elementForm = {\n        id: item.id,\n        name: item.name,\n        element: item.element,\n        type: item.type,\n        funType: item.funType\n      }\n      this.elementFormModal = true\n\n      // 清除表单验证状态\n      this.$nextTick(() => {\n        if (this.$refs.elementForm) {\n          this.$refs.elementForm.clearValidate()\n        }\n      })\n    },\n\n    // 删除字段\n    handleDeleteElement(item) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        content: `确定要删除字段\"${item.name}\"吗？`,\n        onOk: async () => {\n          try {\n            const response = await funElementApi.delete(item.id)\n            if (response.code === 0) {\n              this.$Message.success('删除成功')\n              this.loadElementData()\n            } else {\n              this.$Message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除字段失败:', error)\n            this.$Message.error('删除失败')\n          }\n        }\n      })\n    },\n\n    // 关闭字段管理弹窗\n    handleElementModalCancel() {\n      this.elementManageModal = false\n    },\n\n    // 提交字段表单\n    async handleElementFormSubmit() {\n      console.log('提交表单，当前表单数据:', this.elementForm)\n      this.$refs.elementForm.validate(async (valid) => {\n        console.log('表单验证结果:', valid)\n        if (valid) {\n          this.elementFormLoading = true\n          try {\n            const isEdit = !!this.elementForm.id\n            let response\n\n            if (isEdit) {\n              response = await funElementApi.update(this.elementForm.id, this.elementForm)\n            } else {\n              response = await funElementApi.create(this.elementForm)\n            }\n\n            if (response.code === 0) {\n              this.$Message.success(isEdit ? '更新成功' : '添加成功')\n              this.elementFormModal = false\n              this.loadElementData()\n            } else {\n              this.$Message.error(response.message || '操作失败')\n            }\n          } catch (error) {\n            console.error('保存字段失败:', error)\n            this.$Message.error('操作失败')\n          } finally {\n            this.elementFormLoading = false\n          }\n        }\n      })\n    },\n\n    // 取消字段表单\n    handleElementFormCancel() {\n      this.elementFormModal = false\n      // 重置表单数据和验证状态\n      this.$nextTick(() => {\n        if (this.$refs.elementForm) {\n          this.$refs.elementForm.resetFields()\n        }\n      })\n    },\n\n    // 工作流部署按钮\n    handleDeploy(row) {\n      if (!row.bpmnXml) {\n        this.$Message.warning('该工作流没有BPMN文件，无法部署')\n        return\n      }\n      this.$Modal.confirm({\n        title: '部署确认',\n        content: `确定要部署 \"${row.workflowName}\" 到工作流引擎吗？`,\n        onOk: async () => {\n          try {\n            this.$Message.loading('正在部署工作流...')\n            const response = await workflowApi.erpDeployWorkflow(row)\n\n            if (response.code === 0) {\n              this.$Message.success('工作流部署成功！')\n              console.log('部署结果:', response.data)\n\n              // 可以在这里更新表格数据，标记为已部署\n              this.loadTableData()\n            } else {\n              this.$Message.error(response.message || '工作流部署失败')\n            }\n          } catch (error) {\n            console.error('工作流部署失败:', error)\n            this.$Message.error('工作流部署失败: ' + (error.message || '未知错误'))\n          }\n        }\n      })\n    },\n\n    // 分页变更\n    handlePageChange(page) {\n      this.pageInfo.page = Number(page)\n      this.loadTableData()\n    },\n\n    // 页面大小变更\n    handlePageSizeChange(pageSize) {\n      this.pageInfo.limit = Number(pageSize)\n      this.pageInfo.page = 1\n      this.loadTableData()\n    },\n\n    // ========== 流程功能管理相关方法 ==========\n\n    // 打开流程功能管理弹窗\n    handleFunctionManage() {\n      this.functionModalVisible = true\n      this.loadFunctionData()\n    },\n\n    // 加载功能数据\n    async loadFunctionData() {\n      this.functionLoading = true\n\n      try {\n        const response = await workflowApi.getFunctionTypeList()\n        if (response.code === 0) {\n          this.functionData = response.data.records || []\n        } else {\n          this.$Message.error(response.message || '获取功能类型列表失败')\n        }\n      } catch (error) {\n        console.error('获取功能类型列表失败:', error)\n        this.$Message.error('获取功能类型列表失败')\n      } finally {\n        this.functionLoading = false\n      }\n    },\n\n    // 新增功能\n    handleFunctionAdd() {\n      this.functionEditMode = 'add'\n      this.functionForm = {\n        id: null,\n        key: '',\n        name: ''\n      }\n      this.functionEditModalVisible = true\n      this.$nextTick(() => {\n        this.$refs.functionForm.resetFields()\n      })\n    },\n\n    // 编辑功能\n    handleFunctionEdit(row) {\n      this.functionEditMode = 'edit'\n      this.functionForm = {\n        id: row.id,\n        key: row.key,\n        name: row.name\n      }\n      this.functionEditModalVisible = true\n    },\n\n    // 保存功能\n    handleFunctionSave() {\n      this.$refs.functionForm.validate(async (valid) => {\n        if (valid) {\n          try {\n            let response\n            if (this.functionEditMode === 'add') {\n              // 新增功能\n              response = await workflowApi.addFunctionType(this.functionForm)\n            } else {\n              // 编辑功能\n              response = await workflowApi.updateFunctionType(this.functionForm)\n            }\n\n            if (response.code === 0) {\n              this.$Message.success(response.message || (this.functionEditMode === 'add' ? '功能新增成功' : '功能编辑成功'))\n              this.functionEditModalVisible = false\n              // 重新加载数据\n              this.loadFunctionData()\n            } else {\n              this.$Message.error(response.message || '操作失败')\n            }\n          } catch (error) {\n            console.error('保存功能失败:', error)\n            this.$Message.error('操作失败，请重试')\n          }\n        }\n      })\n    },\n\n    // 删除功能\n    handleFunctionDelete(row) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        content: `确定要删除功能\"${row.name}\"吗？此操作不可恢复。`,\n        onOk: async () => {\n          try {\n            const response = await workflowApi.deleteFunctionType(row.id)\n            if (response.code === 0) {\n              this.$Message.success(response.message || '功能删除成功')\n              // 重新加载数据\n              this.loadFunctionData()\n            } else {\n              this.$Message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除功能失败:', error)\n            this.$Message.error('删除失败，请重试')\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n\n\n<style lang=\"less\" scoped>\n.workflow-list {\n  padding: 16px;\n\n  .search-form {\n    margin-bottom: 16px;\n\n    .ivu-form-item {\n      margin-bottom: 0;\n      margin-right: 16px;\n    }\n  }\n\n  .action-buttons {\n    margin-bottom: 16px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 8px;\n\n    .right-buttons {\n      display: flex;\n      gap: 8px;\n    }\n  }\n\n  .pagination-wrapper {\n    margin-top: 16px;\n    text-align: right;\n  }\n\n  .workflow-view {\n    .workflow-info {\n      margin-bottom: 16px;\n\n      .info-item {\n        margin-bottom: 12px;\n        display: flex;\n        align-items: center;\n\n        .info-label {\n          font-weight: 500;\n          color: #515a6e;\n          min-width: 80px;\n        }\n\n        .info-value {\n          color: #17233d;\n        }\n      }\n    }\n\n    .workflow-preview {\n      .preview-canvas {\n        position: relative;\n        width: 100%;\n        height: 400px;\n        background: #f8f9fa;\n        border: 1px solid #e8eaec;\n        border-radius: 4px;\n        overflow: hidden;\n\n        .preview-node {\n          position: absolute;\n          width: 50px;\n          height: 25px;\n          background: white;\n          border: 1px solid #dcdee2;\n          border-radius: 3px;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          font-size: 10px;\n          user-select: none;\n\n          &.node-start {\n            background: #52c41a;\n            color: white;\n            border-color: #52c41a;\n          }\n\n          &.node-end {\n            background: #f5222d;\n            color: white;\n            border-color: #f5222d;\n          }\n\n          &.node-approval {\n            background: #1890ff;\n            color: white;\n            border-color: #1890ff;\n          }\n\n          &.node-condition {\n            background: #fa8c16;\n            color: white;\n            border-color: #fa8c16;\n            transform: rotate(45deg);\n\n            .node-title {\n              transform: rotate(-45deg);\n            }\n          }\n\n          &.node-task {\n            background: #f0f0f0;\n            color: #333;\n          }\n\n          .node-title {\n            font-size: 8px;\n            font-weight: 500;\n            text-align: center;\n            line-height: 1;\n          }\n\n          i {\n            font-size: 10px;\n            margin-bottom: 2px;\n          }\n        }\n\n        .preview-connections {\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          pointer-events: none;\n        }\n      }\n    }\n  }\n\n  // 流程功能管理样式\n  .function-manage {\n    .function-actions {\n      display: flex;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n  }\n}\n\n// 全局样式覆盖\n:deep(.ivu-card-body) {\n  padding: 16px;\n}\n\n:deep(.ivu-table-wrapper) {\n  border: 1px solid #e8eaec;\n}\n\n// BPMN XML查看弹窗样式\n.bpmn-xml-content {\n  .xml-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #e8eaec;\n\n    .workflow-name {\n      font-size: 16px;\n      font-weight: 600;\n      color: #2d8cf0;\n    }\n  }\n\n  .xml-viewer {\n    max-height: 500px;\n    overflow: auto;\n    background: #f8f9fa;\n    border: 1px solid #e8eaec;\n    border-radius: 4px;\n    padding: 16px;\n\n    pre {\n      margin: 0;\n      white-space: pre-wrap;\n      word-wrap: break-word;\n      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;\n      font-size: 12px;\n      line-height: 1.5;\n      color: #333;\n\n      code {\n        background: none;\n        padding: 0;\n        border: none;\n        font-size: inherit;\n        color: inherit;\n      }\n    }\n\n    // 滚动条样式\n    &::-webkit-scrollbar {\n      width: 8px;\n      height: 8px;\n    }\n\n    &::-webkit-scrollbar-track {\n      background: #f1f1f1;\n      border-radius: 4px;\n    }\n\n    &::-webkit-scrollbar-thumb {\n      background: #c1c1c1;\n      border-radius: 4px;\n\n      &:hover {\n        background: #a8a8a8;\n      }\n    }\n  }\n\n  // 上新字段管理样式\n  .element-manage-content {\n    min-height: 400px;\n  }\n\n  .element-list {\n    padding: 16px 0;\n  }\n\n  .add-element-btn {\n    margin-bottom: 16px;\n  }\n}\n\n\n</style>\n"], "mappings": ";;;;;;;AAoPA,OAAAA,WAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACA;MACAC,UAAA;QACAC,YAAA;QACAC,OAAA;MACA;MAEA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAC,QAAA;MACA,GACA;QACAL,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;QACAG,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,IAAAA,MAAA,CAAAC,GAAA,CAAAC,OAAA;YACA,OAAAH,CAAA;cACAI,KAAA;gBACAV,IAAA;gBACAW,IAAA;cACA;cACAC,EAAA;gBACAC,KAAA,WAAAA,MAAA;kBACAnB,KAAA,CAAAoB,cAAA,CAAAP,MAAA,CAAAC,GAAA;gBACA;cACA;YACA;UACA;YACA,OAAAF,CAAA;UACA;QACA;MACA,GACA;QACAP,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;QACAa,IAAA;MACA,GACA;QACAhB,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;QACAa,IAAA;MACA,GACA;QACAhB,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;QACAa,IAAA;MACA,EACA;MAEA;MACAC,SAAA;MAEA;MACAC,OAAA;MAEA;MACAC,QAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,eAAA;MAEA;MACAC,oBAAA;MACAC,mBAAA;MACAC,cAAA;MAEA;MACAC,kBAAA;MACAC,iBAAA;MAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,kBAAA;MACAC,WAAA;QACAC,EAAA;QACAzC,IAAA;QACA0C,OAAA;QACAlC,IAAA;QACAmC,OAAA;MACA;;MACA;MACAC,cAAA,GACA;QACArC,KAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAC,QAAA;MACA,GACA;QACAL,KAAA;QACAI,GAAA;QACAC,QAAA;QACAC,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,OAAAD,CAAA;YACA+B,KAAA;cACAC,UAAA;cACAC,OAAA;cACAC,YAAA;cACAC,QAAA;cACAC,UAAA;YACA;UACA,GAAAnC,MAAA,CAAAC,GAAA,CAAA0B,OAAA;QACA;MACA,GACA;QACAnC,KAAA;QACAI,GAAA;QACAF,KAAA;QACAI,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,OAAAD,CAAA,SAAAC,MAAA,CAAAC,GAAA,CAAAmC,UAAA,GAAApC,MAAA,CAAAC,GAAA,CAAAmC,UAAA;QACA;MACA,GACA;QACA5C,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;QACAG,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,OAAAD,CAAA,SACAA,CAAA;YACAI,KAAA;cACAV,IAAA;cACAW,IAAA;YACA;YACA0B,KAAA;cACAO,WAAA;YACA;YACAhC,EAAA;cACAC,KAAA,WAAAA,MAAA;gBACAnB,KAAA,CAAAmD,iBAAA,CAAAtC,MAAA,CAAAC,GAAA;cACA;YACA;UACA,IACA,KACA,GACAF,CAAA;YACAI,KAAA;cACAV,IAAA;cACAW,IAAA;YACA;YACAC,EAAA;cACAC,KAAA,WAAAA,MAAA;gBACAnB,KAAA,CAAAoD,mBAAA,CAAAvC,MAAA,CAAAC,GAAA;cACA;YACA;UACA,IACA,KACA,EACA;QACA;MACA,EACA;MACAuC,gBAAA;QACAvD,IAAA,GACA;UAAAwD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,OAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlD,IAAA,GACA;UAAAgD,QAAA;UAAAhD,IAAA;UAAAiD,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAE,oBAAA;MACAC,wBAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;MACAC,YAAA;MACAC,YAAA;QACAxB,EAAA;QACA9B,GAAA;QACAX,IAAA;MACA;MACAkE,aAAA;QACAvD,GAAA,GACA;UAAA6C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1D,IAAA,GACA;UAAAwD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAS,eAAA,GACA;QACA5D,KAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAC,QAAA;MACA,GACA;QACAL,KAAA;QACAI,GAAA;QACAC,QAAA;MACA,GACA;QACAL,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAF,KAAA;QACAC,KAAA;QACAa,IAAA;MACA;IAEA;EACA;EAEA6C,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,YAAA7B,WAAA,CAAAC,EAAA;IACA;EACA;EAEA6B,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EAEAC,OAAA;IACA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAA9D,MAAA,EAAA+D,QAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAhD,OAAA;cAAAwD,QAAA,CAAAC,IAAA;cAEAnE,MAAA;gBACAY,IAAA,EAAA8C,MAAA,CAAA/C,QAAA,CAAAC,IAAA;gBACAR,IAAA,EAAAsD,MAAA,CAAA/C,QAAA,CAAAE,KAAA;gBACAxB,YAAA,EAAAqE,MAAA,CAAAtE,UAAA,CAAAC,YAAA;gBACAC,OAAA,EAAAoE,MAAA,CAAAtE,UAAA,CAAAE;cACA;cAAA4E,QAAA,CAAAE,IAAA;cAAA,OAEArF,WAAA,CAAAsF,eAAA,CAAArE,MAAA;YAAA;cAAA+D,QAAA,GAAAG,QAAA,CAAAI,IAAA;cACA,IAAAP,QAAA,CAAAQ,IAAA;gBACAb,MAAA,CAAAjD,SAAA,GAAAsD,QAAA,CAAA7E,IAAA,CAAAsF,OAAA;gBACAd,MAAA,CAAA/C,QAAA,CAAAG,KAAA,GAAA2D,MAAA,CAAAV,QAAA,CAAA7E,IAAA,CAAA4B,KAAA;cACA;gBACA4C,MAAA,CAAAgB,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;cACA;cAAAwB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAU,EAAA,GAAAV,QAAA;cAEAW,OAAA,CAAAF,KAAA,eAAAT,QAAA,CAAAU,EAAA;cACAlB,MAAA,CAAAgB,QAAA,CAAAC,KAAA;YAAA;cAAAT,QAAA,CAAAC,IAAA;cAEAT,MAAA,CAAAhD,OAAA;cAAA,OAAAwD,QAAA,CAAAY,MAAA;YAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAa,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IAEA;IAEA;IACAkB,YAAA,WAAAA,aAAA;MACAH,OAAA,CAAAI,GAAA,eAAA7F,UAAA;MACA,KAAAuB,QAAA,CAAAC,IAAA;MACA,KAAA4C,aAAA;IACA;IAEA;IACA0B,WAAA,WAAAA,YAAA;MACA,KAAA9F,UAAA;QACAC,YAAA;QACAC,OAAA;MACA;MACA,KAAA0F,YAAA;IACA;IAEA;IACAG,aAAA,WAAAA,cAAA;MACA,KAAA3B,aAAA;IACA;IAEA;IACA4B,SAAA,WAAAA,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IAEA;IACA/E,cAAA,WAAAA,eAAAN,GAAA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACA,KAAAe,mBAAA,GAAAhB,GAAA;QACA,KAAAiB,cAAA,GAAAjB,GAAA,CAAAC,OAAA;QACA,KAAAc,oBAAA;MACA;QACA,KAAA0D,QAAA,CAAAa,OAAA;MACA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA,CAAAC,SAAA,IAAAD,SAAA,CAAAC,SAAA,CAAAC,SAAA;QACAF,SAAA,CAAAC,SAAA,CAAAC,SAAA,MAAA1E,cAAA,EAAA2E,IAAA;UACAJ,MAAA,CAAAf,QAAA,CAAAoB,OAAA;QACA,GAAAC,KAAA;UACAN,MAAA,CAAAf,QAAA,CAAAC,KAAA;QACA;MACA;QACA;QACA,IAAAqB,QAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,QAAA,CAAAG,KAAA,QAAAjF,cAAA;QACA+E,QAAA,CAAAG,IAAA,CAAAC,WAAA,CAAAL,QAAA;QACAA,QAAA,CAAAM,MAAA;QACA;UACAL,QAAA,CAAAM,WAAA;UACA,KAAA7B,QAAA,CAAAoB,OAAA;QACA,SAAAU,GAAA;UACA,KAAA9B,QAAA,CAAAC,KAAA;QACA;QACAsB,QAAA,CAAAG,IAAA,CAAAK,WAAA,CAAAT,QAAA;MACA;IACA;IAEA;IACAU,UAAA,WAAAA,WAAAzG,GAAA;MACA,KAAAoF,OAAA,CAAAC,IAAA,uBAAAqB,MAAA,CAAA1G,GAAA,CAAAyB,EAAA;IACA;IAEA;IACAkF,YAAA,WAAAA,aAAA3G,GAAA;MAAA,IAAA4G,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACAvH,KAAA;QACAwH,OAAA,qCAAAL,MAAA,CAAA1G,GAAA,CAAAZ,YAAA;QACA4H,IAAA;UAAA,IAAAC,KAAA,GAAAvD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsD,SAAA;YAAA,IAAApD,QAAA;YAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAoD,UAAAC,SAAA;cAAA,kBAAAA,SAAA,CAAAlD,IAAA,GAAAkD,SAAA,CAAAjD,IAAA;gBAAA;kBAAAiD,SAAA,CAAAlD,IAAA;kBAAAkD,SAAA,CAAAjD,IAAA;kBAAA,OAEArF,WAAA,CAAAuI,cAAA,CAAArH,GAAA,CAAAyB,EAAA;gBAAA;kBAAAqC,QAAA,GAAAsD,SAAA,CAAA/C,IAAA;kBACA,IAAAP,QAAA,CAAAQ,IAAA;oBACAsC,MAAA,CAAAnC,QAAA,CAAAoB,OAAA,CAAA/B,QAAA,CAAArB,OAAA;oBACA;oBACAmE,MAAA,CAAArD,aAAA;kBACA;oBACAqD,MAAA,CAAAnC,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;kBACA;kBAAA2E,SAAA,CAAAjD,IAAA;kBAAA;gBAAA;kBAAAiD,SAAA,CAAAlD,IAAA;kBAAAkD,SAAA,CAAAzC,EAAA,GAAAyC,SAAA;kBAEAxC,OAAA,CAAAF,KAAA,aAAA0C,SAAA,CAAAzC,EAAA;kBACAiC,MAAA,CAAAnC,QAAA,CAAAC,KAAA;gBAAA;gBAAA;kBAAA,OAAA0C,SAAA,CAAAtC,IAAA;cAAA;YAAA,GAAAoC,QAAA;UAAA,CAEA;UAAA,SAAAF,KAAA;YAAA,OAAAC,KAAA,CAAAK,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAAP,IAAA;QAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAxH,GAAA;MAAA,IAAAyH,MAAA;MAAA,OAAA/D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA8D,SAAA;QAAA,IAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA,EAAA/D,QAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAEAyD,SAAA,GAAA3H,GAAA,CAAAX,OAAA;cACAuI,UAAA,GAAAD,SAAA;cAEAE,UAAA;gBACApG,EAAA,EAAAzB,GAAA,CAAAyB,EAAA;gBACApC,OAAA,EAAAsI;cACA;cAAAI,SAAA,CAAA5D,IAAA;cAAA,OAEArF,WAAA,CAAAkJ,cAAA,CAAAH,UAAA;YAAA;cAAA/D,QAAA,GAAAiE,SAAA,CAAA1D,IAAA;cACA,IAAAP,QAAA,CAAAQ,IAAA;gBACAmD,MAAA,CAAAhD,QAAA,CAAAoB,OAAA,IAAAa,MAAA,CAAAkB,UAAA;gBACA;gBACA5H,GAAA,CAAAX,OAAA,GAAAsI,SAAA;cACA;gBACAF,MAAA,CAAAhD,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA,OAAAiE,MAAA,CAAAkB,UAAA;cACA;cAAAG,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAApD,EAAA,GAAAoD,SAAA;cAEAnD,OAAA,CAAAF,KAAA,YAAAqD,SAAA,CAAApD,EAAA;cACA8C,MAAA,CAAAhD,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAqD,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA4C,QAAA;MAAA;IAEA;IAEA;IAEA;IACAO,mBAAA,WAAAA,oBAAA;MACA,KAAA/G,kBAAA;MACA,KAAAC,iBAAA;MACA,KAAA+G,eAAA;IACA;IAEA;IACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MAAA,OAAAzE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAwE,SAAA;QAAA,IAAAtE,QAAA,EAAAuE,WAAA;QAAA,OAAA1E,mBAAA,GAAAI,IAAA,UAAAuE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAApE,IAAA;YAAA;cAAAoE,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAApE,IAAA;cAAA,OAEApF,aAAA,CAAAyJ,OAAA;gBACA7G,OAAA;cACA;YAAA;cAFAmC,QAAA,GAAAyE,SAAA,CAAAlE,IAAA;cAIA,IAAAP,QAAA,CAAAQ,IAAA;gBACA+D,WAAA,GAAAvE,QAAA,CAAA7E,IAAA,QACA;gBACAkJ,MAAA,CAAA/G,gBAAA,GAAAiH,WAAA,CAAAI,MAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAlJ,IAAA;gBAAA;gBACA2I,MAAA,CAAA9G,mBAAA,GAAAgH,WAAA,CAAAI,MAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAlJ,IAAA;gBAAA;cACA;gBACA2I,MAAA,CAAA1D,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;cACA;cAAA8F,SAAA,CAAApE,IAAA;cAAA;YAAA;cAAAoE,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAA5D,EAAA,GAAA4D,SAAA;cAEA3D,OAAA,CAAAF,KAAA,cAAA6D,SAAA,CAAA5D,EAAA;cACAwD,MAAA,CAAA1D,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA6D,SAAA,CAAAzD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA;IAEA;IAEA;IACAO,uBAAA,WAAAA,wBAAA3J,IAAA;MACA,KAAAmC,iBAAA,GAAAnC,IAAA;IACA;IAEA;IACA4J,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAArH,WAAA;QACAC,EAAA;QACAzC,IAAA;QACA0C,OAAA;QACAlC,IAAA,EAAAsJ,QAAA,MAAA3H,iBAAA;QACAQ,OAAA;MACA;MACA,KAAAL,gBAAA;;MAEA;MACA,KAAAyH,SAAA;QACA,IAAAF,MAAA,CAAAG,KAAA,CAAAxH,WAAA;UACAqH,MAAA,CAAAG,KAAA,CAAAxH,WAAA,CAAAyH,aAAA;QACA;MACA;IACA;IAEA;IACA5G,iBAAA,WAAAA,kBAAAqG,IAAA;MAAA,IAAAQ,MAAA;MACA,KAAA1H,WAAA;QACAC,EAAA,EAAAiH,IAAA,CAAAjH,EAAA;QACAzC,IAAA,EAAA0J,IAAA,CAAA1J,IAAA;QACA0C,OAAA,EAAAgH,IAAA,CAAAhH,OAAA;QACAlC,IAAA,EAAAkJ,IAAA,CAAAlJ,IAAA;QACAmC,OAAA,EAAA+G,IAAA,CAAA/G;MACA;MACA,KAAAL,gBAAA;;MAEA;MACA,KAAAyH,SAAA;QACA,IAAAG,MAAA,CAAAF,KAAA,CAAAxH,WAAA;UACA0H,MAAA,CAAAF,KAAA,CAAAxH,WAAA,CAAAyH,aAAA;QACA;MACA;IACA;IAEA;IACA3G,mBAAA,WAAAA,oBAAAoG,IAAA;MAAA,IAAAS,MAAA;MACA,KAAAtC,MAAA,CAAAC,OAAA;QACAvH,KAAA;QACAwH,OAAA,iDAAAL,MAAA,CAAAgC,IAAA,CAAA1J,IAAA;QACAgI,IAAA;UAAA,IAAAoC,MAAA,GAAA1F,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAyF,SAAA;YAAA,IAAAvF,QAAA;YAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuF,UAAAC,SAAA;cAAA,kBAAAA,SAAA,CAAArF,IAAA,GAAAqF,SAAA,CAAApF,IAAA;gBAAA;kBAAAoF,SAAA,CAAArF,IAAA;kBAAAqF,SAAA,CAAApF,IAAA;kBAAA,OAEApF,aAAA,CAAAyK,MAAA,CAAAd,IAAA,CAAAjH,EAAA;gBAAA;kBAAAqC,QAAA,GAAAyF,SAAA,CAAAlF,IAAA;kBACA,IAAAP,QAAA,CAAAQ,IAAA;oBACA6E,MAAA,CAAA1E,QAAA,CAAAoB,OAAA;oBACAsD,MAAA,CAAAjB,eAAA;kBACA;oBACAiB,MAAA,CAAA1E,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;kBACA;kBAAA8G,SAAA,CAAApF,IAAA;kBAAA;gBAAA;kBAAAoF,SAAA,CAAArF,IAAA;kBAAAqF,SAAA,CAAA5E,EAAA,GAAA4E,SAAA;kBAEA3E,OAAA,CAAAF,KAAA,YAAA6E,SAAA,CAAA5E,EAAA;kBACAwE,MAAA,CAAA1E,QAAA,CAAAC,KAAA;gBAAA;gBAAA;kBAAA,OAAA6E,SAAA,CAAAzE,IAAA;cAAA;YAAA,GAAAuE,QAAA;UAAA,CAEA;UAAA,SAAArC,KAAA;YAAA,OAAAoC,MAAA,CAAA9B,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAAP,IAAA;QAAA;MACA;IACA;IAEA;IACAyC,wBAAA,WAAAA,yBAAA;MACA,KAAAvI,kBAAA;IACA;IAEA;IACAwI,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MAAA,OAAAjG,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgG,SAAA;QAAA,OAAAjG,mBAAA,GAAAI,IAAA,UAAA8F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5F,IAAA,GAAA4F,SAAA,CAAA3F,IAAA;YAAA;cACAS,OAAA,CAAAI,GAAA,iBAAA2E,OAAA,CAAAnI,WAAA;cACAmI,OAAA,CAAAX,KAAA,CAAAxH,WAAA,CAAAuI,QAAA;gBAAA,IAAAC,IAAA,GAAAtG,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqG,SAAAC,KAAA;kBAAA,IAAAC,MAAA,EAAArG,QAAA;kBAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqG,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAnG,IAAA,GAAAmG,SAAA,CAAAlG,IAAA;sBAAA;wBACAS,OAAA,CAAAI,GAAA,YAAAkF,KAAA;wBAAA,KACAA,KAAA;0BAAAG,SAAA,CAAAlG,IAAA;0BAAA;wBAAA;wBACAwF,OAAA,CAAApI,kBAAA;wBAAA8I,SAAA,CAAAnG,IAAA;wBAEAiG,MAAA,KAAAR,OAAA,CAAAnI,WAAA,CAAAC,EAAA;wBAAA,KAGA0I,MAAA;0BAAAE,SAAA,CAAAlG,IAAA;0BAAA;wBAAA;wBAAAkG,SAAA,CAAAlG,IAAA;wBAAA,OACApF,aAAA,CAAAuL,MAAA,CAAAX,OAAA,CAAAnI,WAAA,CAAAC,EAAA,EAAAkI,OAAA,CAAAnI,WAAA;sBAAA;wBAAAsC,QAAA,GAAAuG,SAAA,CAAAhG,IAAA;wBAAAgG,SAAA,CAAAlG,IAAA;wBAAA;sBAAA;wBAAAkG,SAAA,CAAAlG,IAAA;wBAAA,OAEApF,aAAA,CAAAwL,MAAA,CAAAZ,OAAA,CAAAnI,WAAA;sBAAA;wBAAAsC,QAAA,GAAAuG,SAAA,CAAAhG,IAAA;sBAAA;wBAGA,IAAAP,QAAA,CAAAQ,IAAA;0BACAqF,OAAA,CAAAlF,QAAA,CAAAoB,OAAA,CAAAsE,MAAA;0BACAR,OAAA,CAAArI,gBAAA;0BACAqI,OAAA,CAAAzB,eAAA;wBACA;0BACAyB,OAAA,CAAAlF,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;wBACA;wBAAA4H,SAAA,CAAAlG,IAAA;wBAAA;sBAAA;wBAAAkG,SAAA,CAAAnG,IAAA;wBAAAmG,SAAA,CAAA1F,EAAA,GAAA0F,SAAA;wBAEAzF,OAAA,CAAAF,KAAA,YAAA2F,SAAA,CAAA1F,EAAA;wBACAgF,OAAA,CAAAlF,QAAA,CAAAC,KAAA;sBAAA;wBAAA2F,SAAA,CAAAnG,IAAA;wBAEAyF,OAAA,CAAApI,kBAAA;wBAAA,OAAA8I,SAAA,CAAAxF,MAAA;sBAAA;sBAAA;wBAAA,OAAAwF,SAAA,CAAAvF,IAAA;oBAAA;kBAAA,GAAAmF,QAAA;gBAAA,CAGA;gBAAA,iBAAAO,EAAA;kBAAA,OAAAR,IAAA,CAAA1C,KAAA,OAAAC,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAuC,SAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA8E,QAAA;MAAA;IACA;IAEA;IACAa,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA,KAAApJ,gBAAA;MACA;MACA,KAAAyH,SAAA;QACA,IAAA2B,OAAA,CAAA1B,KAAA,CAAAxH,WAAA;UACAkJ,OAAA,CAAA1B,KAAA,CAAAxH,WAAA,CAAAmJ,WAAA;QACA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA5K,GAAA;MAAA,IAAA6K,OAAA;MACA,KAAA7K,GAAA,CAAAC,OAAA;QACA,KAAAwE,QAAA,CAAAa,OAAA;QACA;MACA;MACA,KAAAuB,MAAA,CAAAC,OAAA;QACAvH,KAAA;QACAwH,OAAA,sCAAAL,MAAA,CAAA1G,GAAA,CAAAZ,YAAA;QACA4H,IAAA;UAAA,IAAA8D,MAAA,GAAApH,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmH,SAAA;YAAA,IAAAjH,QAAA;YAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiH,UAAAC,SAAA;cAAA,kBAAAA,SAAA,CAAA/G,IAAA,GAAA+G,SAAA,CAAA9G,IAAA;gBAAA;kBAAA8G,SAAA,CAAA/G,IAAA;kBAEA2G,OAAA,CAAApG,QAAA,CAAAhE,OAAA;kBAAAwK,SAAA,CAAA9G,IAAA;kBAAA,OACArF,WAAA,CAAAoM,iBAAA,CAAAlL,GAAA;gBAAA;kBAAA8D,QAAA,GAAAmH,SAAA,CAAA5G,IAAA;kBAEA,IAAAP,QAAA,CAAAQ,IAAA;oBACAuG,OAAA,CAAApG,QAAA,CAAAoB,OAAA;oBACAjB,OAAA,CAAAI,GAAA,UAAAlB,QAAA,CAAA7E,IAAA;;oBAEA;oBACA4L,OAAA,CAAAtH,aAAA;kBACA;oBACAsH,OAAA,CAAApG,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;kBACA;kBAAAwI,SAAA,CAAA9G,IAAA;kBAAA;gBAAA;kBAAA8G,SAAA,CAAA/G,IAAA;kBAAA+G,SAAA,CAAAtG,EAAA,GAAAsG,SAAA;kBAEArG,OAAA,CAAAF,KAAA,aAAAuG,SAAA,CAAAtG,EAAA;kBACAkG,OAAA,CAAApG,QAAA,CAAAC,KAAA,gBAAAuG,SAAA,CAAAtG,EAAA,CAAAlC,OAAA;gBAAA;gBAAA;kBAAA,OAAAwI,SAAA,CAAAnG,IAAA;cAAA;YAAA,GAAAiG,QAAA;UAAA,CAEA;UAAA,SAAA/D,KAAA;YAAA,OAAA8D,MAAA,CAAAxD,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAAP,IAAA;QAAA;MACA;IACA;IAEA;IACAmE,gBAAA,WAAAA,iBAAAxK,IAAA;MACA,KAAAD,QAAA,CAAAC,IAAA,GAAA6D,MAAA,CAAA7D,IAAA;MACA,KAAA4C,aAAA;IACA;IAEA;IACA6H,oBAAA,WAAAA,qBAAAC,QAAA;MACA,KAAA3K,QAAA,CAAAE,KAAA,GAAA4D,MAAA,CAAA6G,QAAA;MACA,KAAA3K,QAAA,CAAAC,IAAA;MACA,KAAA4C,aAAA;IACA;IAEA;IAEA;IACA+H,oBAAA,WAAAA,qBAAA;MACA,KAAA1I,oBAAA;MACA,KAAA2I,gBAAA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,OAAA9H,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6H,SAAA;QAAA,IAAA3H,QAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA2H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzH,IAAA,GAAAyH,SAAA,CAAAxH,IAAA;YAAA;cACAqH,OAAA,CAAAzI,eAAA;cAAA4I,SAAA,CAAAzH,IAAA;cAAAyH,SAAA,CAAAxH,IAAA;cAAA,OAGArF,WAAA,CAAA8M,mBAAA;YAAA;cAAA9H,QAAA,GAAA6H,SAAA,CAAAtH,IAAA;cACA,IAAAP,QAAA,CAAAQ,IAAA;gBACAkH,OAAA,CAAAxI,YAAA,GAAAc,QAAA,CAAA7E,IAAA,CAAAsF,OAAA;cACA;gBACAiH,OAAA,CAAA/G,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;cACA;cAAAkJ,SAAA,CAAAxH,IAAA;cAAA;YAAA;cAAAwH,SAAA,CAAAzH,IAAA;cAAAyH,SAAA,CAAAhH,EAAA,GAAAgH,SAAA;cAEA/G,OAAA,CAAAF,KAAA,gBAAAiH,SAAA,CAAAhH,EAAA;cACA6G,OAAA,CAAA/G,QAAA,CAAAC,KAAA;YAAA;cAAAiH,SAAA,CAAAzH,IAAA;cAEAsH,OAAA,CAAAzI,eAAA;cAAA,OAAA4I,SAAA,CAAA9G,MAAA;YAAA;YAAA;cAAA,OAAA8G,SAAA,CAAA7G,IAAA;UAAA;QAAA,GAAA2G,QAAA;MAAA;IAEA;IAEA;IACAI,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,KAAAhJ,gBAAA;MACA,KAAAG,YAAA;QACAxB,EAAA;QACA9B,GAAA;QACAX,IAAA;MACA;MACA,KAAA6D,wBAAA;MACA,KAAAkG,SAAA;QACA+C,OAAA,CAAA9C,KAAA,CAAA/F,YAAA,CAAA0H,WAAA;MACA;IACA;IAEA;IACAoB,kBAAA,WAAAA,mBAAA/L,GAAA;MACA,KAAA8C,gBAAA;MACA,KAAAG,YAAA;QACAxB,EAAA,EAAAzB,GAAA,CAAAyB,EAAA;QACA9B,GAAA,EAAAK,GAAA,CAAAL,GAAA;QACAX,IAAA,EAAAgB,GAAA,CAAAhB;MACA;MACA,KAAA6D,wBAAA;IACA;IAEA;IACAmJ,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAAjD,KAAA,CAAA/F,YAAA,CAAA8G,QAAA;QAAA,IAAAmC,KAAA,GAAAxI,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAuI,UAAAjC,KAAA;UAAA,IAAApG,QAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqI,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAnI,IAAA,GAAAmI,UAAA,CAAAlI,IAAA;cAAA;gBAAA,KACA+F,KAAA;kBAAAmC,UAAA,CAAAlI,IAAA;kBAAA;gBAAA;gBAAAkI,UAAA,CAAAnI,IAAA;gBAAA,MAGA+H,OAAA,CAAAnJ,gBAAA;kBAAAuJ,UAAA,CAAAlI,IAAA;kBAAA;gBAAA;gBAAAkI,UAAA,CAAAlI,IAAA;gBAAA,OAEArF,WAAA,CAAAwN,eAAA,CAAAL,OAAA,CAAAhJ,YAAA;cAAA;gBAAAa,QAAA,GAAAuI,UAAA,CAAAhI,IAAA;gBAAAgI,UAAA,CAAAlI,IAAA;gBAAA;cAAA;gBAAAkI,UAAA,CAAAlI,IAAA;gBAAA,OAGArF,WAAA,CAAAyN,kBAAA,CAAAN,OAAA,CAAAhJ,YAAA;cAAA;gBAAAa,QAAA,GAAAuI,UAAA,CAAAhI,IAAA;cAAA;gBAGA,IAAAP,QAAA,CAAAQ,IAAA;kBACA2H,OAAA,CAAAxH,QAAA,CAAAoB,OAAA,CAAA/B,QAAA,CAAArB,OAAA,KAAAwJ,OAAA,CAAAnJ,gBAAA;kBACAmJ,OAAA,CAAApJ,wBAAA;kBACA;kBACAoJ,OAAA,CAAAV,gBAAA;gBACA;kBACAU,OAAA,CAAAxH,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;gBACA;gBAAA4J,UAAA,CAAAlI,IAAA;gBAAA;cAAA;gBAAAkI,UAAA,CAAAnI,IAAA;gBAAAmI,UAAA,CAAA1H,EAAA,GAAA0H,UAAA;gBAEAzH,OAAA,CAAAF,KAAA,YAAA2H,UAAA,CAAA1H,EAAA;gBACAsH,OAAA,CAAAxH,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAA2H,UAAA,CAAAvH,IAAA;YAAA;UAAA,GAAAqH,SAAA;QAAA,CAGA;QAAA,iBAAAK,GAAA;UAAA,OAAAN,KAAA,CAAA5E,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAkF,oBAAA,WAAAA,qBAAAzM,GAAA;MAAA,IAAA0M,OAAA;MACA,KAAA7F,MAAA,CAAAC,OAAA;QACAvH,KAAA;QACAwH,OAAA,iDAAAL,MAAA,CAAA1G,GAAA,CAAAhB,IAAA;QACAgI,IAAA;UAAA,IAAA2F,MAAA,GAAAjJ,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgJ,UAAA;YAAA,IAAA9I,QAAA;YAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8I,WAAAC,UAAA;cAAA,kBAAAA,UAAA,CAAA5I,IAAA,GAAA4I,UAAA,CAAA3I,IAAA;gBAAA;kBAAA2I,UAAA,CAAA5I,IAAA;kBAAA4I,UAAA,CAAA3I,IAAA;kBAAA,OAEArF,WAAA,CAAAiO,kBAAA,CAAA/M,GAAA,CAAAyB,EAAA;gBAAA;kBAAAqC,QAAA,GAAAgJ,UAAA,CAAAzI,IAAA;kBACA,IAAAP,QAAA,CAAAQ,IAAA;oBACAoI,OAAA,CAAAjI,QAAA,CAAAoB,OAAA,CAAA/B,QAAA,CAAArB,OAAA;oBACA;oBACAiK,OAAA,CAAAnB,gBAAA;kBACA;oBACAmB,OAAA,CAAAjI,QAAA,CAAAC,KAAA,CAAAZ,QAAA,CAAArB,OAAA;kBACA;kBAAAqK,UAAA,CAAA3I,IAAA;kBAAA;gBAAA;kBAAA2I,UAAA,CAAA5I,IAAA;kBAAA4I,UAAA,CAAAnI,EAAA,GAAAmI,UAAA;kBAEAlI,OAAA,CAAAF,KAAA,YAAAoI,UAAA,CAAAnI,EAAA;kBACA+H,OAAA,CAAAjI,QAAA,CAAAC,KAAA;gBAAA;gBAAA;kBAAA,OAAAoI,UAAA,CAAAhI,IAAA;cAAA;YAAA,GAAA8H,SAAA;UAAA,CAEA;UAAA,SAAA5F,KAAA;YAAA,OAAA2F,MAAA,CAAArF,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAAP,IAAA;QAAA;MACA;IACA;EACA;AACA"}]}