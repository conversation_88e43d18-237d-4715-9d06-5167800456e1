package com.sy.lingxing.server.controller;

import com.aimo.common.model.ResultBody;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.reflect.TypeToken;
import com.sy.lingxing.client.model.listing.LxListing;
import com.sy.lingxing.client.param.FbaInventoryParam;
import com.sy.lingxing.client.param.ListingRequestParam;
import com.sy.lingxing.client.service.ILingxingListingClient;
import com.sy.lingxing.server.service.ListingService;
import com.sy.lingxing.server.sync.LxListingSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: chiYe
 */
@Api(value = "领星店铺管理配置", tags = "领星店铺管理配置")
@RestController
@RequestMapping(value = "/listing")
public class ListingController implements ILingxingListingClient {
    /**
     * listing服务.
     */
    @Resource
    private ListingService listingService;
    @Resource
    private LxListingSyncService syncService;

    @ApiOperation(value = "获取分页站点列表", notes = "获取分页站点列表")
    @GetMapping(value = "/listPage")
    public ResultBody<IPage<LxListing>> listPage(ListingRequestParam param) {
        return ResultBody.ok(listingService.listPage(param));
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/exportFile")
    public void exportFile(HttpServletResponse response, ListingRequestParam param) {
        listingService.exportFile(response, param);
    }

    @ApiOperation(value = "同步销售Listing数据", notes = "同步销售Listing数据")
    @PostMapping("/syncListing")
    @ResponseBody
    public ResultBody<Boolean> syncListing(@RequestParam(value = "sid")  Long sid, @RequestParam(value = "sellerSkus")String sellerSkus) {
        return ResultBody.ok(!CollectionUtils.isEmpty(syncService.syncLxListing(sid,StringUtils.toArrayString(sellerSkus, null,"&^&").stream().filter(Objects::nonNull).collect(Collectors.toList()))));
    }

    @ApiOperation(value = "条件查询Listing数据", notes = "条件查询Listing数据")
    @PostMapping("/queryListing")
    @ResponseBody
    public List<LxListing> queryListing(@RequestParam(value = "sid") Long sid, @RequestParam(value = "sellerSkus",required = false)String sellerSkus){
        List<String> sellerSkuList = JSON.parseObject(sellerSkus, new TypeToken<List<String>>() {}.getType());
        if(sellerSkuList == null){
            sellerSkuList = new ArrayList<>();
        }
        return listingService.queryListing(sid, sellerSkuList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
    }

    @ApiOperation(value = "查询领星listing数据", notes = "查询领星listing数据")
    @PostMapping("/queryAsin")
    public List<LxListing> queryAsin(@RequestParam(value = "sid")Long sid, @RequestParam(value = "asin")String asin, @RequestParam(value = "isMain")boolean isMain){
        return listingService.queryAsin(sid, asin,isMain);
    }

    @PostMapping("/productLink")
    @ResponseBody
    public Map<Integer,Object> productLink(@RequestParam(value = "sid") Long sid, @RequestParam(value = "productLinkList")String productLinkList){
        return syncService.productLink(sid, JSON.parseObject(productLinkList,new TypeToken<List<Map<String, String>>>() {}.getType()));
    }

    @PostMapping("/updatePrincipal")
    @ResponseBody
    public Map<Integer,Object> updatePrincipal(@RequestParam(value = "sid") Long sid, @RequestParam(value = "principalList")String principalList){
        return syncService.updatePrincipal(sid, JSON.parseObject(principalList,new TypeToken<List<Map<String, String>>>() {}.getType()));
    }
}
