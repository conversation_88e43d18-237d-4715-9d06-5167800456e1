<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aimo.base.server.mapper.base.BaseFunElementMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.aimo.base.client.model.base.BaseFunElement">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="element" property="element" jdbcType="VARCHAR"/>
        <result column="fun_type" property="funType" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, name, element, fun_type, `type`, create_user, create_time, update_user, update_time, `status`
    </sql>
    
</mapper>
