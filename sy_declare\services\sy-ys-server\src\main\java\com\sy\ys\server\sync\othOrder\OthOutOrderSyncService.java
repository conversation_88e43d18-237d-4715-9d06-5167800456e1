package com.sy.ys.server.sync.othOrder;

import com.sy.ys.client.model.othOrder.YsOthOutOrder;
import com.sy.ys.client.param.othOrder.YsOthOutOrderParam;

import java.util.List;

public interface OthOutOrderSyncService {
    List<YsOthOutOrder> syncYsOthOutOrder(List<String> codeList);

    void syncYsOthOutOrder(String startTime, Long org);

    Long addYsOthOutOrder(YsOthOutOrderParam ysSubOrder);

    boolean delYsOthOutOrder(List<Long> idList);

    YsOthOutOrder getOthOutOrder(Long id);
}
