package com.aimo.base.server.client;

import com.aimo.base.client.model.base.Workflow;
import com.aimo.common.model.ResultBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * ERP工作流客户端
 *
 * <AUTHOR>
 */
@FeignClient(name = "sy-erp-server", path = "/workflow")
public interface ErpWorkflowClient {
    
    /**
     * 启动工作流实例
     *
     * @param processKey 流程Key
     * @param variables 流程变量
     * @return 流程实例ID
     */
    @PostMapping("/start")
    ResultBody<String> startProcess(@RequestParam("processKey") String processKey, 
                                   @RequestBody Map<String, Object> variables);
    
    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param variables 任务变量
     * @return 是否成功
     */
    @PostMapping("/complete")
    ResultBody<Boolean> completeTask(@RequestParam("taskId") String taskId, 
                                    @RequestBody Map<String, Object> variables);
    
    /**
     * 获取用户待办任务
     *
     * @param userId 用户ID
     * @return 待办任务列表
     */
    @GetMapping("/tasks")
    ResultBody<Object> getUserTasks(@RequestParam("userId") String userId);
    
    /**
     * 获取流程实例状态
     *
     * @param processInstanceId 流程实例ID
     * @return 流程状态
     */
    @GetMapping("/status")
    ResultBody<String> getProcessStatus(@RequestParam("processInstanceId") String processInstanceId);
    
    /**
     * 部署流程定义
     *
     * @return 部署ID
     */
    @PostMapping("/deploy")
    Map<String, Object> deployProcess(@RequestBody Workflow workflow);
    
    /**
     * 删除流程部署
     *
     * @param deploymentId 部署ID
     * @return 是否成功
     */
    @PostMapping("/undeploy")
    ResultBody<Boolean> undeployProcess(@RequestParam("deploymentId") String deploymentId);
    
    /**
     * 获取流程历史
     *
     * @param processInstanceId 流程实例ID
     * @return 流程历史
     */
    @GetMapping("/history")
    ResultBody<Object> getProcessHistory(@RequestParam("processInstanceId") String processInstanceId);
}
